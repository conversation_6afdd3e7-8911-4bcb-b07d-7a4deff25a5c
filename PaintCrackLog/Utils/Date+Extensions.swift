//
//  Date+Extensions.swift
//  PaintCrackLog
//
//  Created by tyu<PERSON> on 2025/6/2.
//

import Foundation

extension Date {
    
    // MARK: - Formatters
    static let displayFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter
    }()
    
    static let shortDateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .none
        return formatter
    }()
    
    static let timeFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .none
        formatter.timeStyle = .short
        return formatter
    }()
    
    static let relativeDateFormatter: RelativeDateTimeFormatter = {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .full
        return formatter
    }()
    
    // MARK: - Display Methods
    var displayString: String {
        return Date.displayFormatter.string(from: self)
    }
    
    var shortDateString: String {
        return Date.shortDateFormatter.string(from: self)
    }
    
    var timeString: String {
        return Date.timeFormatter.string(from: self)
    }
    
    var relativeString: String {
        return Date.relativeDateFormatter.localizedString(for: self, relativeTo: Date())
    }
    
    // MARK: - Comparison Methods
    func isSameDay(as date: Date) -> Bool {
        let calendar = Calendar.current
        return calendar.isDate(self, inSameDayAs: date)
    }
    
    func isToday() -> Bool {
        return isSameDay(as: Date())
    }
    
    func isYesterday() -> Bool {
        let calendar = Calendar.current
        guard let yesterday = calendar.date(byAdding: .day, value: -1, to: Date()) else {
            return false
        }
        return isSameDay(as: yesterday)
    }
    
    // MARK: - Time Interval Formatting
    func timeIntervalString(to date: Date) -> String {
        let interval = date.timeIntervalSince(self)
        
        if interval < 60 {
            return "\(Int(interval))s"
        } else if interval < 3600 {
            let minutes = Int(interval / 60)
            return "\(minutes)m"
        } else if interval < 86400 {
            let hours = Int(interval / 3600)
            let minutes = Int((interval.truncatingRemainder(dividingBy: 3600)) / 60)
            return "\(hours)h \(minutes)m"
        } else {
            let days = Int(interval / 86400)
            let hours = Int((interval.truncatingRemainder(dividingBy: 86400)) / 3600)
            return "\(days)d \(hours)h"
        }
    }
}
