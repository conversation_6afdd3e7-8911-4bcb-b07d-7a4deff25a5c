//
//  UIColor+Extensions.swift
//  PaintCrackLog
//
//  Created by tyu<PERSON> on 2025/6/2.
//

import UIKit

extension UIColor {
    
    // MARK: - App Theme Colors
    static let primaryGradientStart = UIColor(red: 0.2, green: 0.6, blue: 1.0, alpha: 1.0) // Bright blue
    static let primaryGradientEnd = UIColor(red: 0.4, green: 0.8, blue: 0.9, alpha: 1.0)   // Light cyan
    
    static let secondaryGradientStart = UIColor(red: 0.9, green: 0.5, blue: 0.8, alpha: 1.0) // Pink
    static let secondaryGradientEnd = UIColor(red: 1.0, green: 0.7, blue: 0.4, alpha: 1.0)   // Orange
    
    static let cardBackground = UIColor.systemBackground.withAlphaComponent(0.95)
    static let cardShadow = UIColor.black.withAlphaComponent(0.1)
    
    // MARK: - Status Colors
    static let statusGreen = UIColor.systemGreen
    static let statusOrange = UIColor.systemOrange
    static let statusBlue = UIColor.systemBlue
    static let statusRed = UIColor.systemRed
    
    // MARK: - Text Colors
    static let primaryText = UIColor.label
    static let secondaryText = UIColor.secondaryLabel
    static let tertiaryText = UIColor.tertiaryLabel
    
    // MARK: - Convenience Initializers
    convenience init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }
        
        self.init(
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue: Double(b) / 255,
            alpha: Double(a) / 255
        )
    }
    
    // MARK: - Color Manipulation
    func lighter(by percentage: CGFloat = 30.0) -> UIColor? {
        return self.adjust(by: abs(percentage))
    }
    
    func darker(by percentage: CGFloat = 30.0) -> UIColor? {
        return self.adjust(by: -1 * abs(percentage))
    }
    
    private func adjust(by percentage: CGFloat = 30.0) -> UIColor? {
        var red: CGFloat = 0, green: CGFloat = 0, blue: CGFloat = 0, alpha: CGFloat = 0
        if self.getRed(&red, green: &green, blue: &blue, alpha: &alpha) {
            return UIColor(red: min(red + percentage/100, 1.0),
                          green: min(green + percentage/100, 1.0),
                          blue: min(blue + percentage/100, 1.0),
                          alpha: alpha)
        } else {
            return nil
        }
    }
}
