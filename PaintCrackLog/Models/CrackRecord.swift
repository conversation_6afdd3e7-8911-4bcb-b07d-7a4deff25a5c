//
//  CrackRecord.swift
//  PaintCrackLog
//
//  Created by tyuu on 2025/6/2.
//

import Foundation
import UIKit

class CrackRecord: NSObject, NSCoding {
    
    // MARK: - Properties
    var id: String
    var paintName: String
    var paintType: PaintType
    var surfaceType: SurfaceType
    var layerThickness: Double? // in mm
    var applicationTime: Date
    var crackStartTime: Date?
    var crackPattern: CrackPattern
    var crackRange: CrackRange
    var crackStatus: CrackStatus
    var notes: String
    var photoPath: String?
    var createdAt: Date
    var updatedAt: Date
    
    // MARK: - Computed Properties
    var timeToCrack: TimeInterval? {
        guard let crackStartTime = crackStartTime else { return nil }
        return crackStartTime.timeIntervalSince(applicationTime)
    }
    
    var formattedTimeToCrack: String {
        guard let timeToCrack = timeToCrack else { return "Not yet cracked" }

        let hours = Int(timeToCrack) / 3600
        let minutes = Int(timeToCrack.truncatingRemainder(dividingBy: 3600)) / 60

        if hours > 24 {
            let days = hours / 24
            let remainingHours = hours % 24
            return "\(days)d \(remainingHours)h"
        } else if hours > 0 {
            return "\(hours)h \(minutes)m"
        } else {
            return "\(minutes)m"
        }
    }
    
    // MARK: - Initialization
    init(paintName: String,
         paintType: PaintType,
         surfaceType: SurfaceType,
         layerThickness: Double? = nil,
         applicationTime: Date = Date(),
         crackStartTime: Date? = nil,
         crackPattern: CrackPattern = .unknown,
         crackRange: CrackRange = .local,
         crackStatus: CrackStatus = .notStarted,
         notes: String = "",
         photoPath: String? = nil) {
        
        self.id = UUID().uuidString
        self.paintName = paintName
        self.paintType = paintType
        self.surfaceType = surfaceType
        self.layerThickness = layerThickness
        self.applicationTime = applicationTime
        self.crackStartTime = crackStartTime
        self.crackPattern = crackPattern
        self.crackRange = crackRange
        self.crackStatus = crackStatus
        self.notes = notes
        self.photoPath = photoPath
        self.createdAt = Date()
        self.updatedAt = Date()
        
        super.init()
    }
    
    // MARK: - NSCoding
    required init?(coder: NSCoder) {
        self.id = coder.decodeObject(forKey: "id") as? String ?? UUID().uuidString
        self.paintName = coder.decodeObject(forKey: "paintName") as? String ?? ""
        
        if let paintTypeRaw = coder.decodeObject(forKey: "paintType") as? String {
            self.paintType = PaintType(rawValue: paintTypeRaw) ?? .other
        } else {
            self.paintType = .other
        }
        
        if let surfaceTypeRaw = coder.decodeObject(forKey: "surfaceType") as? String {
            self.surfaceType = SurfaceType(rawValue: surfaceTypeRaw) ?? .other
        } else {
            self.surfaceType = .other
        }
        
        self.layerThickness = coder.decodeObject(forKey: "layerThickness") as? Double
        self.applicationTime = coder.decodeObject(forKey: "applicationTime") as? Date ?? Date()
        self.crackStartTime = coder.decodeObject(forKey: "crackStartTime") as? Date
        
        if let crackPatternRaw = coder.decodeObject(forKey: "crackPattern") as? String {
            self.crackPattern = CrackPattern(rawValue: crackPatternRaw) ?? .unknown
        } else {
            self.crackPattern = .unknown
        }
        
        if let crackRangeRaw = coder.decodeObject(forKey: "crackRange") as? String {
            self.crackRange = CrackRange(rawValue: crackRangeRaw) ?? .local
        } else {
            self.crackRange = .local
        }
        
        if let crackStatusRaw = coder.decodeObject(forKey: "crackStatus") as? String {
            self.crackStatus = CrackStatus(rawValue: crackStatusRaw) ?? .notStarted
        } else {
            self.crackStatus = .notStarted
        }
        
        self.notes = coder.decodeObject(forKey: "notes") as? String ?? ""
        self.photoPath = coder.decodeObject(forKey: "photoPath") as? String
        self.createdAt = coder.decodeObject(forKey: "createdAt") as? Date ?? Date()
        self.updatedAt = coder.decodeObject(forKey: "updatedAt") as? Date ?? Date()
        
        super.init()
    }
    
    func encode(with coder: NSCoder) {
        coder.encode(id, forKey: "id")
        coder.encode(paintName, forKey: "paintName")
        coder.encode(paintType.rawValue, forKey: "paintType")
        coder.encode(surfaceType.rawValue, forKey: "surfaceType")
        coder.encode(layerThickness, forKey: "layerThickness")
        coder.encode(applicationTime, forKey: "applicationTime")
        coder.encode(crackStartTime, forKey: "crackStartTime")
        coder.encode(crackPattern.rawValue, forKey: "crackPattern")
        coder.encode(crackRange.rawValue, forKey: "crackRange")
        coder.encode(crackStatus.rawValue, forKey: "crackStatus")
        coder.encode(notes, forKey: "notes")
        coder.encode(photoPath, forKey: "photoPath")
        coder.encode(createdAt, forKey: "createdAt")
        coder.encode(updatedAt, forKey: "updatedAt")
    }
    
    // MARK: - Helper Methods
    func updateTimestamp() {
        updatedAt = Date()
    }
}
