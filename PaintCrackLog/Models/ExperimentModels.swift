//
//  ExperimentModels.swift
//  PaintCrackLog
//
//  Created by tyu<PERSON> on 2025/6/2.
//

import Foundation

// MARK: - Experiment Group
class ExperimentGroup: NSObject, NSCoding {
    
    var id: String
    var name: String
    var experimentDescription: String
    var createdAt: Date
    var updatedAt: Date
    var experimentSamples: [ExperimentSample]

    init(name: String, experimentDescription: String = "") {
        self.id = UUID().uuidString
        self.name = name
        self.experimentDescription = experimentDescription
        self.createdAt = Date()
        self.updatedAt = Date()
        self.experimentSamples = []
        super.init()
    }
    
    // MARK: - NSCoding
    required init?(coder: NSCoder) {
        self.id = coder.decodeObject(forKey: "id") as? String ?? UUID().uuidString
        self.name = coder.decodeObject(forKey: "name") as? String ?? ""
        self.experimentDescription = coder.decodeObject(forKey: "experimentDescription") as? String ?? ""
        self.createdAt = coder.decodeObject(forKey: "createdAt") as? Date ?? Date()
        self.updatedAt = coder.decodeObject(forKey: "updatedAt") as? Date ?? Date()
        self.experimentSamples = coder.decodeObject(forKey: "experimentSamples") as? [ExperimentSample] ?? []
        super.init()
    }

    func encode(with coder: NSCoder) {
        coder.encode(id, forKey: "id")
        coder.encode(name, forKey: "name")
        coder.encode(experimentDescription, forKey: "experimentDescription")
        coder.encode(createdAt, forKey: "createdAt")
        coder.encode(updatedAt, forKey: "updatedAt")
        coder.encode(experimentSamples, forKey: "experimentSamples")
    }
    
    func updateTimestamp() {
        updatedAt = Date()
    }
}

// MARK: - Experiment Sample
class ExperimentSample: NSObject, NSCoding {
    
    var id: String
    var name: String
    var sampleType: SampleType
    var baseRecord: CrackRecord?
    var environmentalConditions: EnvironmentalConditions
    var notes: String
    var createdAt: Date
    var updatedAt: Date
    
    init(name: String, sampleType: SampleType, baseRecord: CrackRecord? = nil) {
        self.id = UUID().uuidString
        self.name = name
        self.sampleType = sampleType
        self.baseRecord = baseRecord
        self.environmentalConditions = EnvironmentalConditions()
        self.notes = ""
        self.createdAt = Date()
        self.updatedAt = Date()
        super.init()
    }
    
    // MARK: - NSCoding
    required init?(coder: NSCoder) {
        self.id = coder.decodeObject(forKey: "id") as? String ?? UUID().uuidString
        self.name = coder.decodeObject(forKey: "name") as? String ?? ""
        
        if let sampleTypeRaw = coder.decodeObject(forKey: "sampleType") as? String {
            self.sampleType = SampleType(rawValue: sampleTypeRaw) ?? .experimental
        } else {
            self.sampleType = .experimental
        }
        
        self.baseRecord = coder.decodeObject(forKey: "baseRecord") as? CrackRecord
        self.environmentalConditions = coder.decodeObject(forKey: "environmentalConditions") as? EnvironmentalConditions ?? EnvironmentalConditions()
        self.notes = coder.decodeObject(forKey: "notes") as? String ?? ""
        self.createdAt = coder.decodeObject(forKey: "createdAt") as? Date ?? Date()
        self.updatedAt = coder.decodeObject(forKey: "updatedAt") as? Date ?? Date()
        super.init()
    }
    
    func encode(with coder: NSCoder) {
        coder.encode(id, forKey: "id")
        coder.encode(name, forKey: "name")
        coder.encode(sampleType.rawValue, forKey: "sampleType")
        coder.encode(baseRecord, forKey: "baseRecord")
        coder.encode(environmentalConditions, forKey: "environmentalConditions")
        coder.encode(notes, forKey: "notes")
        coder.encode(createdAt, forKey: "createdAt")
        coder.encode(updatedAt, forKey: "updatedAt")
    }
    
    func updateTimestamp() {
        updatedAt = Date()
    }
}

// MARK: - Environmental Conditions
class EnvironmentalConditions: NSObject, NSCoding {
    
    var temperature: Double? // in Celsius
    var humidity: Double? // in percentage
    var layerThickness: Double? // in mm
    var dryingTime: Double? // in hours
    var additionalNotes: String
    
    override init() {
        self.temperature = nil
        self.humidity = nil
        self.layerThickness = nil
        self.dryingTime = nil
        self.additionalNotes = ""
        super.init()
    }
    
    init(temperature: Double?, humidity: Double?, layerThickness: Double?, dryingTime: Double?, additionalNotes: String = "") {
        self.temperature = temperature
        self.humidity = humidity
        self.layerThickness = layerThickness
        self.dryingTime = dryingTime
        self.additionalNotes = additionalNotes
        super.init()
    }
    
    // MARK: - NSCoding
    required init?(coder: NSCoder) {
        self.temperature = coder.decodeObject(forKey: "temperature") as? Double
        self.humidity = coder.decodeObject(forKey: "humidity") as? Double
        self.layerThickness = coder.decodeObject(forKey: "layerThickness") as? Double
        self.dryingTime = coder.decodeObject(forKey: "dryingTime") as? Double
        self.additionalNotes = coder.decodeObject(forKey: "additionalNotes") as? String ?? ""
        super.init()
    }
    
    func encode(with coder: NSCoder) {
        coder.encode(temperature, forKey: "temperature")
        coder.encode(humidity, forKey: "humidity")
        coder.encode(layerThickness, forKey: "layerThickness")
        coder.encode(dryingTime, forKey: "dryingTime")
        coder.encode(additionalNotes, forKey: "additionalNotes")
    }
}

// MARK: - Sample Type
enum SampleType: String, CaseIterable {
    case experimental = "Experimental"
    case control = "Control"
    
    var displayName: String {
        return rawValue
    }
    
    var color: String {
        switch self {
        case .experimental:
            return "systemBlue"
        case .control:
            return "systemOrange"
        }
    }
    
    var icon: String {
        switch self {
        case .experimental:
            return "flask"
        case .control:
            return "checkmark.shield"
        }
    }
}
