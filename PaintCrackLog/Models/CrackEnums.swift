//
//  CrackEnums.swift
//  PaintCrackLog
//
//  Created by tyuu on 2025/6/2.
//

import Foundation

// MARK: - Paint Type
enum PaintType: String, CaseIterable {
    case acrylic = "Acrylic"
    case latex = "Latex"
    case mineral = "Mineral"
    case oil = "Oil"
    case watercolor = "Watercolor"
    case enamel = "Enamel"
    case other = "Other"
    
    var displayName: String {
        return rawValue
    }
}

// MARK: - Surface Type
enum SurfaceType: String, CaseIterable {
    case canvas = "Canvas"
    case wall = "Wall"
    case wood = "Wood"
    case paper = "Paper"
    case metal = "Metal"
    case plastic = "Plastic"
    case concrete = "Concrete"
    case other = "Other"
    
    var displayName: String {
        return rawValue
    }
}

// MARK: - Crack Pattern
enum CrackPattern: String, CaseIterable {
    case linear = "Linear"
    case network = "Network"
    case scale = "Scale"
    case dotted = "Dotted"
    case unknown = "Unknown"
    
    var displayName: String {
        return rawValue
    }
    
    var icon: String {
        switch self {
        case .linear:
            return "line.diagonal"
        case .network:
            return "grid"
        case .scale:
            return "hexagon"
        case .dotted:
            return "circle.dotted"
        case .unknown:
            return "questionmark"
        }
    }
}

// MARK: - Crack Range
enum CrackRange: String, CaseIterable {
    case local = "Local"
    case comprehensive = "Comprehensive"
    case edge = "Edge"
    case center = "Center"
    
    var displayName: String {
        return rawValue
    }
    
    var description: String {
        switch self {
        case .local:
            return "Localized cracking in specific areas"
        case .comprehensive:
            return "Widespread cracking across the surface"
        case .edge:
            return "Cracking primarily at edges and borders"
        case .center:
            return "Cracking concentrated in central areas"
        }
    }
}

// MARK: - Crack Status
enum CrackStatus: String, CaseIterable {
    case notStarted = "Not Started"
    case inProgress = "In Progress"
    case stabilized = "Stabilized"
    case worsening = "Worsening"
    
    var displayName: String {
        return rawValue
    }
    
    var color: String {
        switch self {
        case .notStarted:
            return "systemGreen"
        case .inProgress:
            return "systemOrange"
        case .stabilized:
            return "systemBlue"
        case .worsening:
            return "systemRed"
        }
    }
}
