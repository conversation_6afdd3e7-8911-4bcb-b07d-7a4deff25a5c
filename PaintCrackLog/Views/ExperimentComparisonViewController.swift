//
//  ExperimentComparisonViewController.swift
//  PaintCrackLog
//
//  Created by tyu<PERSON> on 2025/6/2.
//

import UIKit
import SnapKit

class ExperimentComparisonViewController: UIViewController {
    
    // MARK: - Properties
    private let experimentGroup: ExperimentGroup
    
    // MARK: - UI Components
    private lazy var gradientView: GradientView = {
        let view = GradientView(
            startColor: .primaryGradientStart,
            endColor: .primaryGradientEnd,
            direction: .topToBottom
        )
        return view
    }()
    
    private lazy var scrollView: UIScrollView = {
        let scroll = UIScrollView()
        scroll.backgroundColor = .clear
        scroll.showsVerticalScrollIndicator = false
        return scroll
    }()
    
    private lazy var contentView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    private lazy var comparisonStackView: UIStackView = {
        let stack = UIStackView()
        stack.axis = .vertical
        stack.spacing = 20
        stack.distribution = .fill
        return stack
    }()
    
    // MARK: - Initialization
    init(experimentGroup: ExperimentGroup) {
        self.experimentGroup = experimentGroup
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupComparisons()
    }
    
    // MARK: - Setup
    private func setupUI() {
        title = "Comparison Results"
        view.backgroundColor = .systemBackground
        
        // Navigation items
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .done,
            target: self,
            action: #selector(doneButtonTapped)
        )
        
        // Add views
        view.addSubview(gradientView)
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        contentView.addSubview(comparisonStackView)
        
        // Constraints
        gradientView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        comparisonStackView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().offset(-20)
        }
    }
    
    private func setupComparisons() {
        // Group samples by type
        let experimentalSamples = experimentGroup.experimentSamples.filter { $0.sampleType == .experimental }
        let controlSamples = experimentGroup.experimentSamples.filter { $0.sampleType == .control }
        
        // Create header
        let headerView = createHeaderView()
        comparisonStackView.addArrangedSubview(headerView)
        
        // Create comparison cards for each pair
        let maxCount = max(experimentalSamples.count, controlSamples.count)
        
        for i in 0..<maxCount {
            let experimentalSample = i < experimentalSamples.count ? experimentalSamples[i] : nil
            let controlSample = i < controlSamples.count ? controlSamples[i] : nil
            
            let comparisonCard = createComparisonCard(
                experimental: experimentalSample,
                control: controlSample,
                index: i + 1
            )
            
            comparisonStackView.addArrangedSubview(comparisonCard)
        }
        
        // Add summary if we have both types
        if !experimentalSamples.isEmpty && !controlSamples.isEmpty {
            let summaryView = createSummaryView(experimental: experimentalSamples, control: controlSamples)
            comparisonStackView.addArrangedSubview(summaryView)
        }
    }
    
    private func createHeaderView() -> UIView {
        let container = UIView()
        container.backgroundColor = .cardBackground
        container.layer.cornerRadius = 16
        container.layer.shadowColor = UIColor.cardShadow.cgColor
        container.layer.shadowOffset = CGSize(width: 0, height: 2)
        container.layer.shadowRadius = 8
        container.layer.shadowOpacity = 1.0
        
        let titleLabel = UILabel()
        titleLabel.text = experimentGroup.name
        titleLabel.font = .systemFont(ofSize: 20, weight: .bold)
        titleLabel.textColor = .primaryText
        titleLabel.textAlignment = .center
        
        let subtitleLabel = UILabel()
        subtitleLabel.text = "Side-by-Side Comparison"
        subtitleLabel.font = .systemFont(ofSize: 14, weight: .medium)
        subtitleLabel.textColor = .secondaryText
        subtitleLabel.textAlignment = .center
        
        container.addSubview(titleLabel)
        container.addSubview(subtitleLabel)
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(4)
            make.leading.trailing.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().offset(-20)
        }
        
        return container
    }
    
    private func createComparisonCard(experimental: ExperimentSample?, control: ExperimentSample?, index: Int) -> UIView {
        let container = UIView()
        container.backgroundColor = .cardBackground
        container.layer.cornerRadius = 16
        container.layer.shadowColor = UIColor.cardShadow.cgColor
        container.layer.shadowOffset = CGSize(width: 0, height: 2)
        container.layer.shadowRadius = 8
        container.layer.shadowOpacity = 1.0
        
        let titleLabel = UILabel()
        titleLabel.text = "Comparison #\(index)"
        titleLabel.font = .systemFont(ofSize: 16, weight: .semibold)
        titleLabel.textColor = .primaryText
        titleLabel.textAlignment = .center
        
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.distribution = .fillEqually
        stackView.spacing = 16
        
        // Experimental side
        let expView = createSampleComparisonView(sample: experimental, type: .experimental)
        stackView.addArrangedSubview(expView)
        
        // Control side
        let ctrlView = createSampleComparisonView(sample: control, type: .control)
        stackView.addArrangedSubview(ctrlView)
        
        container.addSubview(titleLabel)
        container.addSubview(stackView)
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        stackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16)
        }
        
        return container
    }
    
    private func createSampleComparisonView(sample: ExperimentSample?, type: SampleType) -> UIView {
        let container = UIView()
        container.backgroundColor = type == .experimental ? UIColor.systemBlue.withAlphaComponent(0.1) : UIColor.systemOrange.withAlphaComponent(0.1)
        container.layer.cornerRadius = 12
        
        let headerLabel = UILabel()
        headerLabel.text = type.displayName
        headerLabel.font = .systemFont(ofSize: 14, weight: .bold)
        headerLabel.textColor = type == .experimental ? .systemBlue : .systemOrange
        headerLabel.textAlignment = .center
        
        let contentLabel = UILabel()
        contentLabel.numberOfLines = 0
        contentLabel.font = .systemFont(ofSize: 12, weight: .regular)
        contentLabel.textColor = .secondaryText
        
        if let sample = sample {
            var content = "Name: \(sample.name)\n"
            
            let conditions = sample.environmentalConditions
            if let temp = conditions.temperature {
                content += "Temp: \(temp)°C\n"
            }
            if let humidity = conditions.humidity {
                content += "Humidity: \(humidity)%\n"
            }
            if let thickness = conditions.layerThickness {
                content += "Thickness: \(thickness)mm\n"
            }
            if let dryingTime = conditions.dryingTime {
                content += "Drying: \(dryingTime)h\n"
            }
            
            // Add crack information if available from base record
            if let baseRecord = sample.baseRecord {
                content += "\nCrack Pattern: \(baseRecord.crackPattern.displayName)\n"
                content += "Crack Range: \(baseRecord.crackRange.displayName)\n"
                content += "Status: \(baseRecord.crackStatus.displayName)\n"
                if let crackTime = baseRecord.crackStartTime {
                    content += "Time to Crack: \(baseRecord.formattedTimeToCrack)"
                }
            }
            
            contentLabel.text = content.trimmingCharacters(in: .whitespacesAndNewlines)
        } else {
            contentLabel.text = "No sample"
            contentLabel.textColor = .tertiaryText
            contentLabel.textAlignment = .center
        }
        
        container.addSubview(headerLabel)
        container.addSubview(contentLabel)
        
        headerLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.leading.trailing.equalToSuperview().inset(8)
        }
        
        contentLabel.snp.makeConstraints { make in
            make.top.equalTo(headerLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(8)
            make.bottom.equalToSuperview().offset(-12)
        }
        
        return container
    }
    
    private func createSummaryView(experimental: [ExperimentSample], control: [ExperimentSample]) -> UIView {
        let container = UIView()
        container.backgroundColor = .cardBackground
        container.layer.cornerRadius = 16
        container.layer.shadowColor = UIColor.cardShadow.cgColor
        container.layer.shadowOffset = CGSize(width: 0, height: 2)
        container.layer.shadowRadius = 8
        container.layer.shadowOpacity = 1.0
        
        let titleLabel = UILabel()
        titleLabel.text = "Summary"
        titleLabel.font = .systemFont(ofSize: 18, weight: .bold)
        titleLabel.textColor = .primaryText
        titleLabel.textAlignment = .center
        
        let summaryLabel = UILabel()
        summaryLabel.numberOfLines = 0
        summaryLabel.font = .systemFont(ofSize: 14, weight: .regular)
        summaryLabel.textColor = .secondaryText
        
        var summaryText = "Experimental Samples: \(experimental.count)\n"
        summaryText += "Control Samples: \(control.count)\n\n"
        
        // Calculate averages if available
        let expTemps = experimental.compactMap { $0.environmentalConditions.temperature }
        let ctrlTemps = control.compactMap { $0.environmentalConditions.temperature }
        
        if !expTemps.isEmpty && !ctrlTemps.isEmpty {
            let expAvgTemp = expTemps.reduce(0, +) / Double(expTemps.count)
            let ctrlAvgTemp = ctrlTemps.reduce(0, +) / Double(ctrlTemps.count)
            summaryText += "Avg Temperature:\nExp: \(String(format: "%.1f", expAvgTemp))°C, Ctrl: \(String(format: "%.1f", ctrlAvgTemp))°C\n\n"
        }
        
        summaryText += "This comparison helps identify how different conditions affect crack formation patterns."
        
        summaryLabel.text = summaryText
        
        container.addSubview(titleLabel)
        container.addSubview(summaryLabel)
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        summaryLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().offset(-20)
        }
        
        return container
    }
    
    // MARK: - Actions
    @objc private func doneButtonTapped() {
        dismiss(animated: true)
    }
}
