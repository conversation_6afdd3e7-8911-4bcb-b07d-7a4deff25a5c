//
//  CrackRecordSelectorViewController.swift
//  PaintCrackLog
//
//  Created by tyu<PERSON> on 2025/6/2.
//

import UIKit
import SnapKit

class CrackRecordSelectorViewController: UIViewController {
    
    // MARK: - Properties
    private let crackRecords: [CrackRecord]
    private let onSelection: (CrackRecord) -> Void
    
    // MARK: - UI Components
    private lazy var gradientView: GradientView = {
        let view = GradientView(
            startColor: .primaryGradientStart,
            endColor: .primaryGradientEnd,
            direction: .topToBottom
        )
        return view
    }()
    
    private lazy var tableView: UITableView = {
        let table = UITableView(frame: .zero, style: .plain)
        table.delegate = self
        table.dataSource = self
        table.backgroundColor = .clear
        table.separatorStyle = .none
        table.showsVerticalScrollIndicator = false
        table.register(CrackRecordCell.self, forCellReuseIdentifier: CrackRecordCell.identifier)
        table.contentInset = UIEdgeInsets(top: 20, left: 0, bottom: 20, right: 0)
        return table
    }()
    
    // MARK: - Initialization
    init(crackRecords: [CrackRecord], onSelection: @escaping (CrackRecord) -> Void) {
        self.crackRecords = crackRecords
        self.onSelection = onSelection
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }
    
    // MARK: - Setup
    private func setupUI() {
        title = "Select Record"
        view.backgroundColor = .systemBackground
        
        // Navigation items
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .cancel,
            target: self,
            action: #selector(cancelButtonTapped)
        )
        
        // Add views
        view.addSubview(gradientView)
        view.addSubview(tableView)
        
        gradientView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        tableView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
    }
    
    // MARK: - Actions
    @objc private func cancelButtonTapped() {
        dismiss(animated: true)
    }
}

// MARK: - UITableViewDataSource
extension CrackRecordSelectorViewController: UITableViewDataSource {
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return crackRecords.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: CrackRecordCell.identifier, for: indexPath) as! CrackRecordCell
        cell.configure(with: crackRecords[indexPath.row])
        return cell
    }
}

// MARK: - UITableViewDelegate
extension CrackRecordSelectorViewController: UITableViewDelegate {
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 120
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        let selectedRecord = crackRecords[indexPath.row]
        dismiss(animated: true) { [weak self] in
            self?.onSelection(selectedRecord)
        }
    }
}
