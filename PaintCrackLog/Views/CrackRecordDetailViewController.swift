//
//  CrackRecordDetailViewController.swift
//  PaintCrackLog
//
//  Created by tyu<PERSON> on 2025/6/2.
//

import UIKit
import SnapKit

protocol CrackRecordDetailDelegate: AnyObject {
    func didSaveRecord(_ record: CrackRecord)
}

class CrackRecordDetailViewController: UIViewController {
    
    // MARK: - Properties
    weak var delegate: CrackRecordDetailDelegate?
    private var record: CrackRecord?
    private var isEditMode: Bool = false
    
    // MARK: - UI Components
    private lazy var gradientView: GradientView = {
        let view = GradientView(
            startColor: .secondaryGradientStart,
            endColor: .secondaryGradientEnd,
            direction: .topToBottom
        )
        return view
    }()
    
    private lazy var scrollView: UIScrollView = {
        let scroll = UIScrollView()
        scroll.backgroundColor = .clear
        scroll.showsVerticalScrollIndicator = false
        return scroll
    }()
    
    private lazy var contentView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    private lazy var formContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .cardBackground
        view.layer.cornerRadius = 20
        view.layer.shadowColor = UIColor.cardShadow.cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: 4)
        view.layer.shadowRadius = 12
        view.layer.shadowOpacity = 1.0
        return view
    }()
    
    // Form Fields
    private lazy var paintNameTextField: UITextField = {
        let field = createTextField(placeholder: "Paint Name")
        return field
    }()
    
    private lazy var paintTypeButton: UIButton = {
        let button = createSelectionButton(title: "Select Paint Type")
        button.addTarget(self, action: #selector(paintTypeButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var surfaceTypeButton: UIButton = {
        let button = createSelectionButton(title: "Select Surface Type")
        button.addTarget(self, action: #selector(surfaceTypeButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var layerThicknessTextField: UITextField = {
        let field = createTextField(placeholder: "Layer Thickness (mm) - Optional")
        field.keyboardType = .decimalPad
        return field
    }()
    
    private lazy var applicationTimeButton: UIButton = {
        let button = createSelectionButton(title: "Application Time")
        button.addTarget(self, action: #selector(applicationTimeButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var crackStartTimeButton: UIButton = {
        let button = createSelectionButton(title: "Crack Start Time - Optional")
        button.addTarget(self, action: #selector(crackStartTimeButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var crackPatternButton: UIButton = {
        let button = createSelectionButton(title: "Select Crack Pattern")
        button.addTarget(self, action: #selector(crackPatternButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var crackRangeButton: UIButton = {
        let button = createSelectionButton(title: "Select Crack Range")
        button.addTarget(self, action: #selector(crackRangeButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var crackStatusButton: UIButton = {
        let button = createSelectionButton(title: "Select Crack Status")
        button.addTarget(self, action: #selector(crackStatusButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var notesTextView: UITextView = {
        let textView = UITextView()
        textView.backgroundColor = UIColor.systemGray6
        textView.layer.cornerRadius = 12
        textView.font = .systemFont(ofSize: 16)
        textView.textColor = .primaryText
        textView.textContainerInset = UIEdgeInsets(top: 12, left: 12, bottom: 12, right: 12)
        return textView
    }()
    
    private lazy var photoButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Add Photo", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = .systemBlue
        button.layer.cornerRadius = 12
        button.titleLabel?.font = .systemFont(ofSize: 16, weight: .medium)
        button.addTarget(self, action: #selector(photoButtonTapped), for: .touchUpInside)
        return button
    }()

    private lazy var photoImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.layer.cornerRadius = 12
        imageView.clipsToBounds = true
        imageView.backgroundColor = UIColor.systemGray6
        imageView.isHidden = true
        return imageView
    }()
    
    // Selected values
    private var selectedPaintType: PaintType = .acrylic
    private var selectedSurfaceType: SurfaceType = .canvas
    private var selectedApplicationTime: Date = Date()
    private var selectedCrackStartTime: Date?
    private var selectedCrackPattern: CrackPattern = .unknown
    private var selectedCrackRange: CrackRange = .local
    private var selectedCrackStatus: CrackStatus = .notStarted
    private var selectedPhotoPath: String?
    
    // MARK: - Initialization
    init(record: CrackRecord? = nil) {
        self.record = record
        self.isEditMode = record != nil
        super.init(nibName: nil, bundle: nil)
        
        if let record = record {
            loadRecordData(record)
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupKeyboardHandling()
    }

    // MARK: - Setup
    private func setupUI() {
        title = isEditMode ? "Edit Record" : "New Record"
        view.backgroundColor = .systemBackground

        // Navigation items
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .cancel,
            target: self,
            action: #selector(cancelButtonTapped)
        )

        navigationItem.rightBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .save,
            target: self,
            action: #selector(saveButtonTapped)
        )

        // Add views
        view.addSubview(gradientView)
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        contentView.addSubview(formContainerView)

        // Add form fields
        let stackView = UIStackView(arrangedSubviews: [
            createFieldContainer(title: "Paint Name", field: paintNameTextField),
            createFieldContainer(title: "Paint Type", field: paintTypeButton),
            createFieldContainer(title: "Surface Type", field: surfaceTypeButton),
            createFieldContainer(title: "Layer Thickness", field: layerThicknessTextField),
            createFieldContainer(title: "Application Time", field: applicationTimeButton),
            createFieldContainer(title: "Crack Start Time", field: crackStartTimeButton),
            createFieldContainer(title: "Crack Pattern", field: crackPatternButton),
            createFieldContainer(title: "Crack Range", field: crackRangeButton),
            createFieldContainer(title: "Crack Status", field: crackStatusButton),
            createNotesContainer(),
            createPhotoContainer()
        ])

        stackView.axis = .vertical
        stackView.spacing = 20
        stackView.distribution = .fill

        formContainerView.addSubview(stackView)

        // Constraints
        gradientView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        formContainerView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().offset(-20)
        }

        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(20)
        }

        updateButtonTitles()
    }

    private func setupKeyboardHandling() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(keyboardWillShow),
            name: UIResponder.keyboardWillShowNotification,
            object: nil
        )

        NotificationCenter.default.addObserver(
            self,
            selector: #selector(keyboardWillHide),
            name: UIResponder.keyboardWillHideNotification,
            object: nil
        )

        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(dismissKeyboard))
        view.addGestureRecognizer(tapGesture)
    }

    // MARK: - Helper Methods
    private func createTextField(placeholder: String) -> UITextField {
        let field = UITextField()
        field.placeholder = placeholder
        field.backgroundColor = UIColor.systemGray6
        field.layer.cornerRadius = 12
        field.font = .systemFont(ofSize: 16)
        field.textColor = .primaryText
        field.leftView = UIView(frame: CGRect(x: 0, y: 0, width: 12, height: 0))
        field.leftViewMode = .always
        field.rightView = UIView(frame: CGRect(x: 0, y: 0, width: 12, height: 0))
        field.rightViewMode = .always
        return field
    }

    private func createSelectionButton(title: String) -> UIButton {
        let button = UIButton(type: .system)
        button.setTitle(title, for: .normal)
        button.setTitleColor(.primaryText, for: .normal)
        button.backgroundColor = UIColor.systemGray6
        button.layer.cornerRadius = 12
        button.titleLabel?.font = .systemFont(ofSize: 16)
        button.contentHorizontalAlignment = .left
        button.contentEdgeInsets = UIEdgeInsets(top: 0, left: 12, bottom: 0, right: 12)

        let chevron = UIImageView(image: UIImage(systemName: "chevron.right"))
        chevron.tintColor = .secondaryText
        button.addSubview(chevron)
        chevron.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.trailing.equalToSuperview().offset(-12)
            make.width.height.equalTo(16)
        }

        return button
    }

    private func createFieldContainer(title: String, field: UIView) -> UIView {
        let container = UIView()

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = .systemFont(ofSize: 14, weight: .medium)
        titleLabel.textColor = .secondaryText

        container.addSubview(titleLabel)
        container.addSubview(field)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
        }

        field.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalTo(48)
        }

        return container
    }

    private func createNotesContainer() -> UIView {
        let container = UIView()

        let titleLabel = UILabel()
        titleLabel.text = "Notes"
        titleLabel.font = .systemFont(ofSize: 14, weight: .medium)
        titleLabel.textColor = .secondaryText

        container.addSubview(titleLabel)
        container.addSubview(notesTextView)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
        }

        notesTextView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalTo(100)
        }

        return container
    }

    private func createPhotoContainer() -> UIView {
        let container = UIView()

        let titleLabel = UILabel()
        titleLabel.text = "Photo"
        titleLabel.font = .systemFont(ofSize: 14, weight: .medium)
        titleLabel.textColor = .secondaryText

        container.addSubview(titleLabel)
        container.addSubview(photoButton)
        container.addSubview(photoImageView)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
        }

        photoButton.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(50)
        }

        photoImageView.snp.makeConstraints { make in
            make.top.equalTo(photoButton.snp.bottom).offset(8)
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalTo(200)
        }

        return container
    }

    private func loadRecordData(_ record: CrackRecord) {
        paintNameTextField.text = record.paintName
        selectedPaintType = record.paintType
        selectedSurfaceType = record.surfaceType

        if let thickness = record.layerThickness {
            layerThicknessTextField.text = String(thickness)
        }

        selectedApplicationTime = record.applicationTime
        selectedCrackStartTime = record.crackStartTime
        selectedCrackPattern = record.crackPattern
        selectedCrackRange = record.crackRange
        selectedCrackStatus = record.crackStatus
        notesTextView.text = record.notes
        selectedPhotoPath = record.photoPath
    }

    private func updateButtonTitles() {
        paintTypeButton.setTitle(selectedPaintType.displayName, for: .normal)
        surfaceTypeButton.setTitle(selectedSurfaceType.displayName, for: .normal)
        applicationTimeButton.setTitle(selectedApplicationTime.displayString, for: .normal)

        if let crackStartTime = selectedCrackStartTime {
            crackStartTimeButton.setTitle(crackStartTime.displayString, for: .normal)
        } else {
            crackStartTimeButton.setTitle("Not yet cracked", for: .normal)
        }

        crackPatternButton.setTitle(selectedCrackPattern.displayName, for: .normal)
        crackRangeButton.setTitle(selectedCrackRange.displayName, for: .normal)
        crackStatusButton.setTitle(selectedCrackStatus.displayName, for: .normal)

        if let photoPath = selectedPhotoPath {
            photoButton.setTitle("Change Photo", for: .normal)
            photoButton.backgroundColor = .statusGreen

            // Load and display the image
            let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
            let imagePath = documentsPath.appendingPathComponent(photoPath)

            if let imageData = try? Data(contentsOf: imagePath),
               let image = UIImage(data: imageData) {
                photoImageView.image = image
                photoImageView.isHidden = false
            }
        } else {
            photoButton.setTitle("Add Photo", for: .normal)
            photoButton.backgroundColor = .systemBlue
            photoImageView.image = nil
            photoImageView.isHidden = true
        }
    }

    // MARK: - Actions
    @objc private func cancelButtonTapped() {
        dismiss(animated: true)
    }

    @objc private func saveButtonTapped() {
        guard validateForm() else { return }

        let layerThickness = Double(layerThicknessTextField.text ?? "")

        if let existingRecord = record {
            // Update existing record
            existingRecord.paintName = paintNameTextField.text ?? ""
            existingRecord.paintType = selectedPaintType
            existingRecord.surfaceType = selectedSurfaceType
            existingRecord.layerThickness = layerThickness
            existingRecord.applicationTime = selectedApplicationTime
            existingRecord.crackStartTime = selectedCrackStartTime
            existingRecord.crackPattern = selectedCrackPattern
            existingRecord.crackRange = selectedCrackRange
            existingRecord.crackStatus = selectedCrackStatus
            existingRecord.notes = notesTextView.text ?? ""
            existingRecord.photoPath = selectedPhotoPath
            existingRecord.updateTimestamp()

            delegate?.didSaveRecord(existingRecord)
        } else {
            // Create new record
            let newRecord = CrackRecord(
                paintName: paintNameTextField.text ?? "",
                paintType: selectedPaintType,
                surfaceType: selectedSurfaceType,
                layerThickness: layerThickness,
                applicationTime: selectedApplicationTime,
                crackStartTime: selectedCrackStartTime,
                crackPattern: selectedCrackPattern,
                crackRange: selectedCrackRange,
                crackStatus: selectedCrackStatus,
                notes: notesTextView.text ?? "",
                photoPath: selectedPhotoPath
            )

            delegate?.didSaveRecord(newRecord)
        }

        dismiss(animated: true)
    }

    @objc private func paintTypeButtonTapped() {
        showSelectionAlert(
            title: "Select Paint Type",
            options: PaintType.allCases.map { $0.displayName },
            selectedIndex: PaintType.allCases.firstIndex(of: selectedPaintType) ?? 0
        ) { [weak self] index in
            self?.selectedPaintType = PaintType.allCases[index]
            self?.updateButtonTitles()
        }
    }

    @objc private func surfaceTypeButtonTapped() {
        showSelectionAlert(
            title: "Select Surface Type",
            options: SurfaceType.allCases.map { $0.displayName },
            selectedIndex: SurfaceType.allCases.firstIndex(of: selectedSurfaceType) ?? 0
        ) { [weak self] index in
            self?.selectedSurfaceType = SurfaceType.allCases[index]
            self?.updateButtonTitles()
        }
    }

    @objc private func applicationTimeButtonTapped() {
        showDatePicker(
            title: "Application Time",
            currentDate: selectedApplicationTime
        ) { [weak self] date in
            self?.selectedApplicationTime = date
            self?.updateButtonTitles()
        }
    }

    @objc private func crackStartTimeButtonTapped() {
        let alert = UIAlertController(title: "Crack Start Time", message: nil, preferredStyle: .actionSheet)

        alert.addAction(UIAlertAction(title: "Set Time", style: .default) { [weak self] _ in
            self?.showDatePicker(
                title: "Crack Start Time",
                currentDate: self?.selectedCrackStartTime ?? Date()
            ) { date in
                self?.selectedCrackStartTime = date
                self?.updateButtonTitles()
            }
        })

        alert.addAction(UIAlertAction(title: "Clear Time", style: .destructive) { [weak self] _ in
            self?.selectedCrackStartTime = nil
            self?.updateButtonTitles()
        })

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))

        if let popover = alert.popoverPresentationController {
            popover.sourceView = crackStartTimeButton
            popover.sourceRect = crackStartTimeButton.bounds
        }

        present(alert, animated: true)
    }

    @objc private func crackPatternButtonTapped() {
        showSelectionAlert(
            title: "Select Crack Pattern",
            options: CrackPattern.allCases.map { $0.displayName },
            selectedIndex: CrackPattern.allCases.firstIndex(of: selectedCrackPattern) ?? 0
        ) { [weak self] index in
            self?.selectedCrackPattern = CrackPattern.allCases[index]
            self?.updateButtonTitles()
        }
    }

    @objc private func crackRangeButtonTapped() {
        showSelectionAlert(
            title: "Select Crack Range",
            options: CrackRange.allCases.map { $0.displayName },
            selectedIndex: CrackRange.allCases.firstIndex(of: selectedCrackRange) ?? 0
        ) { [weak self] index in
            self?.selectedCrackRange = CrackRange.allCases[index]
            self?.updateButtonTitles()
        }
    }

    @objc private func crackStatusButtonTapped() {
        showSelectionAlert(
            title: "Select Crack Status",
            options: CrackStatus.allCases.map { $0.displayName },
            selectedIndex: CrackStatus.allCases.firstIndex(of: selectedCrackStatus) ?? 0
        ) { [weak self] index in
            self?.selectedCrackStatus = CrackStatus.allCases[index]
            self?.updateButtonTitles()
        }
    }

    @objc private func photoButtonTapped() {
        let alert = UIAlertController(title: "Add Photo", message: nil, preferredStyle: .actionSheet)

        alert.addAction(UIAlertAction(title: "Camera", style: .default) { [weak self] _ in
            self?.presentImagePicker(sourceType: .camera)
        })

        alert.addAction(UIAlertAction(title: "Photo Library", style: .default) { [weak self] _ in
            self?.presentImagePicker(sourceType: .photoLibrary)
        })

        if selectedPhotoPath != nil {
            alert.addAction(UIAlertAction(title: "Remove Photo", style: .destructive) { [weak self] _ in
                self?.selectedPhotoPath = nil
                self?.updateButtonTitles()
            })
        }

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))

        if let popover = alert.popoverPresentationController {
            popover.sourceView = photoButton
            popover.sourceRect = photoButton.bounds
        }

        present(alert, animated: true)
    }

    @objc private func dismissKeyboard() {
        view.endEditing(true)
    }

    @objc private func keyboardWillShow(notification: NSNotification) {
        guard let keyboardFrame = notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? CGRect else { return }

        let keyboardHeight = keyboardFrame.height
        scrollView.contentInset.bottom = keyboardHeight
        scrollView.scrollIndicatorInsets.bottom = keyboardHeight
    }

    @objc private func keyboardWillHide(notification: NSNotification) {
        scrollView.contentInset.bottom = 0
        scrollView.scrollIndicatorInsets.bottom = 0
    }

    // MARK: - Helper Methods
    private func validateForm() -> Bool {
        guard let paintName = paintNameTextField.text, !paintName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            showAlert(title: "Error", message: "Please enter a paint name.")
            return false
        }

        return true
    }

    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }

    private func showSelectionAlert(title: String, options: [String], selectedIndex: Int, completion: @escaping (Int) -> Void) {
        let alert = UIAlertController(title: title, message: nil, preferredStyle: .actionSheet)

        for (index, option) in options.enumerated() {
            let action = UIAlertAction(title: option, style: .default) { _ in
                completion(index)
            }

            if index == selectedIndex {
                action.setValue(true, forKey: "checked")
            }

            alert.addAction(action)
        }

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))

        if let popover = alert.popoverPresentationController {
            popover.sourceView = view
            popover.sourceRect = CGRect(x: view.bounds.midX, y: view.bounds.midY, width: 0, height: 0)
            popover.permittedArrowDirections = []
        }

        present(alert, animated: true)
    }

    private func showDatePicker(title: String, currentDate: Date, completion: @escaping (Date) -> Void) {
        let alert = UIAlertController(title: title, message: "\n\n\n\n\n\n", preferredStyle: .alert)

        let datePicker = UIDatePicker()
        datePicker.datePickerMode = .dateAndTime
        datePicker.preferredDatePickerStyle = .wheels
        datePicker.date = currentDate

        alert.setValue(datePicker, forKey: "contentViewController")

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        alert.addAction(UIAlertAction(title: "Select", style: .default) { _ in
            completion(datePicker.date)
        })

        present(alert, animated: true)
    }

    private func presentImagePicker(sourceType: UIImagePickerController.SourceType) {
        guard UIImagePickerController.isSourceTypeAvailable(sourceType) else {
            showAlert(title: "Error", message: "This source type is not available.")
            return
        }

        let picker = UIImagePickerController()
        picker.sourceType = sourceType
        picker.delegate = self
        picker.allowsEditing = true

        present(picker, animated: true)
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - UIImagePickerControllerDelegate
extension CrackRecordDetailViewController: UIImagePickerControllerDelegate, UINavigationControllerDelegate {

    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
        picker.dismiss(animated: true)

        guard let image = info[.editedImage] as? UIImage ?? info[.originalImage] as? UIImage else {
            return
        }

        // Save image to documents directory
        if let imageData = image.jpegData(compressionQuality: 0.8) {
            let filename = "\(UUID().uuidString).jpg"
            let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
            let imagePath = documentsPath.appendingPathComponent(filename)

            do {
                try imageData.write(to: imagePath)
                selectedPhotoPath = filename
                updateButtonTitles()
            } catch {
                showAlert(title: "Error", message: "Failed to save image.")
            }
        }
    }

    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
        picker.dismiss(animated: true)
    }
}
