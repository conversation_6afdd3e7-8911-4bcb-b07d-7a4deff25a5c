//
//  CrackRecordListViewController.swift
//  PaintCrackLog
//
//  Created by tyuu on 2025/6/2.
//

import UIKit
import SnapKit

class CrackRecordListViewController: UIViewController {
    
    // MARK: - Properties
    private var crackRecords: [CrackRecord] = []
    private let userDefaults = UserDefaults.standard
    private let recordsKey = "CrackRecords"
    
    // MARK: - UI Components
    private lazy var gradientView: GradientView = {
        let view = GradientView(
            startColor: .primaryGradientStart,
            endColor: .primaryGradientEnd,
            direction: .topToBottom
        )
        return view
    }()
    
    private lazy var tableView: UITableView = {
        let table = UITableView(frame: .zero, style: .plain)
        table.delegate = self
        table.dataSource = self
        table.backgroundColor = .clear
        table.separatorStyle = .none
        table.showsVerticalScrollIndicator = false
        table.register(CrackRecordCell.self, forCellReuseIdentifier: CrackRecordCell.identifier)
        table.contentInset = UIEdgeInsets(top: 20, left: 0, bottom: 20, right: 0)
        return table
    }()
    
    private lazy var emptyStateView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        view.isHidden = true
        
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "paintbrush.pointed")
        imageView.tintColor = .white.withAlphaComponent(0.6)
        imageView.contentMode = .scaleAspectFit
        
        let titleLabel = UILabel()
        titleLabel.text = "No Crack Records"
        titleLabel.font = .systemFont(ofSize: 24, weight: .semibold)
        titleLabel.textColor = .white
        titleLabel.textAlignment = .center
        
        let subtitleLabel = UILabel()
        subtitleLabel.text = "Tap the + button to create your first crack record"
        subtitleLabel.font = .systemFont(ofSize: 16, weight: .regular)
        subtitleLabel.textColor = .white.withAlphaComponent(0.8)
        subtitleLabel.textAlignment = .center
        subtitleLabel.numberOfLines = 0
        
        view.addSubview(imageView)
        view.addSubview(titleLabel)
        view.addSubview(subtitleLabel)
        
        imageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().offset(-60)
            make.width.height.equalTo(80)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(imageView.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        return view
    }()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadRecords()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        loadRecords()
        tableView.reloadData()
        updateEmptyState()
    }
    
    // MARK: - Setup
    private func setupUI() {
        title = "Crack Records"
        view.backgroundColor = .systemBackground
        
        // Add gradient background
        view.addSubview(gradientView)
        view.addSubview(tableView)
        view.addSubview(emptyStateView)
        
        gradientView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        tableView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        emptyStateView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        // Navigation bar setup
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .add,
            target: self,
            action: #selector(addButtonTapped)
        )
        
        navigationController?.navigationBar.tintColor = .white
        navigationController?.navigationBar.titleTextAttributes = [
            .foregroundColor: UIColor.white,
            .font: UIFont.systemFont(ofSize: 18, weight: .semibold)
        ]
    }
    
    // MARK: - Actions
    @objc private func addButtonTapped() {
        let detailVC = CrackRecordDetailViewController()
        detailVC.delegate = self
        let navController = UINavigationController(rootViewController: detailVC)
        present(navController, animated: true)
    }
    
    // MARK: - Data Management
    private func loadRecords() {
        if let data = userDefaults.data(forKey: recordsKey),
           let records = try? NSKeyedUnarchiver.unarchiveTopLevelObjectWithData(data) as? [CrackRecord] {
            crackRecords = records.sorted { $0.createdAt > $1.createdAt }
        }
    }
    
    private func saveRecords() {
        if let data = try? NSKeyedArchiver.archivedData(withRootObject: crackRecords, requiringSecureCoding: false) {
            userDefaults.set(data, forKey: recordsKey)
        }
    }
    
    private func updateEmptyState() {
        emptyStateView.isHidden = !crackRecords.isEmpty
        tableView.isHidden = crackRecords.isEmpty
    }
}

// MARK: - UITableViewDataSource
extension CrackRecordListViewController: UITableViewDataSource {

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return crackRecords.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: CrackRecordCell.identifier, for: indexPath) as! CrackRecordCell
        cell.configure(with: crackRecords[indexPath.row])
        return cell
    }
}

// MARK: - UITableViewDelegate
extension CrackRecordListViewController: UITableViewDelegate {

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 120
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)

        let record = crackRecords[indexPath.row]
        let detailVC = CrackRecordDetailViewController(record: record)
        detailVC.delegate = self
        let navController = UINavigationController(rootViewController: detailVC)
        present(navController, animated: true)
    }

    func tableView(_ tableView: UITableView, commit editingStyle: UITableViewCell.EditingStyle, forRowAt indexPath: IndexPath) {
        if editingStyle == .delete {
            crackRecords.remove(at: indexPath.row)
            saveRecords()
            tableView.deleteRows(at: [indexPath], with: .fade)
            updateEmptyState()
        }
    }
}

// MARK: - CrackRecordDetailDelegate
extension CrackRecordListViewController: CrackRecordDetailDelegate {

    func didSaveRecord(_ record: CrackRecord) {
        if let index = crackRecords.firstIndex(where: { $0.id == record.id }) {
            crackRecords[index] = record
        } else {
            crackRecords.insert(record, at: 0)
        }

        crackRecords.sort { $0.createdAt > $1.createdAt }
        saveRecords()
        tableView.reloadData()
        updateEmptyState()
    }
}
