//
//  ExperimentDetailViewController.swift
//  PaintCrackLog
//
//  Created by tyu<PERSON> on 2025/6/2.
//

import UIKit
import SnapKit

protocol ExperimentDetailDelegate: AnyObject {
    func didUpdateExperimentGroup(_ group: ExperimentGroup)
}

class ExperimentDetailViewController: UIViewController {
    
    // MARK: - Properties
    weak var delegate: ExperimentDetailDelegate?
    private var experimentGroup: ExperimentGroup
    
    // MARK: - UI Components
    private lazy var gradientView: GradientView = {
        let view = GradientView(
            startColor: .primaryGradientStart,
            endColor: .primaryGradientEnd,
            direction: .topToBottom
        )
        return view
    }()
    
    private lazy var scrollView: UIScrollView = {
        let scroll = UIScrollView()
        scroll.backgroundColor = .clear
        scroll.showsVerticalScrollIndicator = false
        return scroll
    }()
    
    private lazy var contentView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    private lazy var headerView: UIView = {
        let view = UIView()
        view.backgroundColor = .cardBackground
        view.layer.cornerRadius = 20
        view.layer.shadowColor = UIColor.cardShadow.cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: 4)
        view.layer.shadowRadius = 12
        view.layer.shadowOpacity = 1.0
        return view
    }()
    
    private lazy var samplesTableView: UITableView = {
        let table = UITableView(frame: .zero, style: .plain)
        table.delegate = self
        table.dataSource = self
        table.backgroundColor = .cardBackground
        table.layer.cornerRadius = 20
        table.separatorStyle = .none
        table.showsVerticalScrollIndicator = false
        table.register(ExperimentSampleCell.self, forCellReuseIdentifier: ExperimentSampleCell.identifier)
        table.isScrollEnabled = false
        return table
    }()
    
    private lazy var nameLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 24, weight: .bold)
        label.textColor = .primaryText
        label.numberOfLines = 0
        return label
    }()
    
    private lazy var descriptionLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16, weight: .regular)
        label.textColor = .secondaryText
        label.numberOfLines = 0
        return label
    }()
    
    private lazy var statsStackView: UIStackView = {
        let stack = UIStackView()
        stack.axis = .horizontal
        stack.distribution = .fillEqually
        stack.spacing = 16
        return stack
    }()
    
    private lazy var addSampleButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Add Sample", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = .systemBlue
        button.layer.cornerRadius = 12
        button.titleLabel?.font = .systemFont(ofSize: 16, weight: .medium)
        button.addTarget(self, action: #selector(addSampleButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var compareButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Compare Results", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = .systemGreen
        button.layer.cornerRadius = 12
        button.titleLabel?.font = .systemFont(ofSize: 16, weight: .medium)
        button.addTarget(self, action: #selector(compareButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // MARK: - Initialization
    init(experimentGroup: ExperimentGroup) {
        self.experimentGroup = experimentGroup
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        updateUI()
    }
    
    // MARK: - Setup
    private func setupUI() {
        title = "Experiment Details"
        view.backgroundColor = .systemBackground
        
        // Navigation items
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .done,
            target: self,
            action: #selector(doneButtonTapped)
        )
        
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .edit,
            target: self,
            action: #selector(editButtonTapped)
        )
        
        // Add views
        view.addSubview(gradientView)
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        
        contentView.addSubview(headerView)
        contentView.addSubview(samplesTableView)
        
        headerView.addSubview(nameLabel)
        headerView.addSubview(descriptionLabel)
        headerView.addSubview(statsStackView)
        headerView.addSubview(addSampleButton)
        headerView.addSubview(compareButton)
        
        setupConstraints()
        setupStatsView()
    }
    
    private func setupConstraints() {
        gradientView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        headerView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        nameLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        descriptionLabel.snp.makeConstraints { make in
            make.top.equalTo(nameLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        statsStackView.snp.makeConstraints { make in
            make.top.equalTo(descriptionLabel.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(60)
        }
        
        addSampleButton.snp.makeConstraints { make in
            make.top.equalTo(statsStackView.snp.bottom).offset(20)
            make.leading.equalToSuperview().offset(20)
            make.height.equalTo(44)
        }
        
        compareButton.snp.makeConstraints { make in
            make.top.equalTo(addSampleButton)
            make.leading.equalTo(addSampleButton.snp.trailing).offset(12)
            make.trailing.equalToSuperview().offset(-20)
            make.height.equalTo(44)
            make.width.equalTo(addSampleButton)
            make.bottom.equalToSuperview().offset(-20)
        }
        
        samplesTableView.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().offset(-20)
            make.height.equalTo(calculateTableHeight())
        }
    }

    private func setupStatsView() {
        let experimentalStat = createStatView(title: "Experimental", value: "0", color: .systemBlue)
        let controlStat = createStatView(title: "Control", value: "0", color: .systemOrange)
        let totalStat = createStatView(title: "Total", value: "0", color: .systemGray)

        statsStackView.addArrangedSubview(experimentalStat)
        statsStackView.addArrangedSubview(controlStat)
        statsStackView.addArrangedSubview(totalStat)
    }

    private func createStatView(title: String, value: String, color: UIColor) -> UIView {
        let container = UIView()
        container.backgroundColor = color.withAlphaComponent(0.1)
        container.layer.cornerRadius = 12

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = .systemFont(ofSize: 12, weight: .medium)
        titleLabel.textColor = color
        titleLabel.textAlignment = .center

        let valueLabel = UILabel()
        valueLabel.text = value
        valueLabel.font = .systemFont(ofSize: 20, weight: .bold)
        valueLabel.textColor = color
        valueLabel.textAlignment = .center

        container.addSubview(titleLabel)
        container.addSubview(valueLabel)

        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(8)
            make.leading.trailing.equalToSuperview().inset(8)
        }

        valueLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(4)
            make.leading.trailing.equalToSuperview().inset(8)
            make.bottom.equalToSuperview().offset(-8)
        }

        return container
    }

    private func calculateTableHeight() -> CGFloat {
        let rowHeight: CGFloat = 80
        let headerHeight: CGFloat = 0
        let footerHeight: CGFloat = 0
        let sampleCount = experimentGroup.experimentSamples.count

        if sampleCount == 0 {
            return 100 // Empty state height
        }

        return CGFloat(sampleCount) * rowHeight + headerHeight + footerHeight
    }

    private func updateUI() {
        nameLabel.text = experimentGroup.name
        descriptionLabel.text = experimentGroup.experimentDescription.isEmpty ? "No description provided" : experimentGroup.experimentDescription

        // Update stats
        let experimentalCount = experimentGroup.experimentSamples.filter { $0.sampleType == .experimental }.count
        let controlCount = experimentGroup.experimentSamples.filter { $0.sampleType == .control }.count
        let totalCount = experimentGroup.experimentSamples.count

        if let experimentalStat = statsStackView.arrangedSubviews[0] as? UIView,
           let valueLabel = experimentalStat.subviews.last as? UILabel {
            valueLabel.text = "\(experimentalCount)"
        }

        if let controlStat = statsStackView.arrangedSubviews[1] as? UIView,
           let valueLabel = controlStat.subviews.last as? UILabel {
            valueLabel.text = "\(controlCount)"
        }

        if let totalStat = statsStackView.arrangedSubviews[2] as? UIView,
           let valueLabel = totalStat.subviews.last as? UILabel {
            valueLabel.text = "\(totalCount)"
        }

        // Update table height
        samplesTableView.snp.updateConstraints { make in
            make.height.equalTo(calculateTableHeight())
        }

        samplesTableView.reloadData()
        compareButton.isEnabled = experimentGroup.experimentSamples.count >= 2
        compareButton.alpha = compareButton.isEnabled ? 1.0 : 0.5
    }

    // MARK: - Actions
    @objc private func doneButtonTapped() {
        delegate?.didUpdateExperimentGroup(experimentGroup)
        dismiss(animated: true)
    }

    @objc private func editButtonTapped() {
        let alert = UIAlertController(title: "Edit Experiment", message: "Update experiment details", preferredStyle: .alert)

        alert.addTextField { textField in
            textField.placeholder = "Experiment name"
            textField.text = self.experimentGroup.name
            textField.autocapitalizationType = .words
        }

        alert.addTextField { textField in
            textField.placeholder = "Description (optional)"
            textField.text = self.experimentGroup.experimentDescription
            textField.autocapitalizationType = .sentences
        }

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        alert.addAction(UIAlertAction(title: "Save", style: .default) { [weak self] _ in
            guard let name = alert.textFields?[0].text, !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
                return
            }

            self?.experimentGroup.name = name
            self?.experimentGroup.experimentDescription = alert.textFields?[1].text ?? ""
            self?.experimentGroup.updateTimestamp()
            self?.updateUI()
        })

        present(alert, animated: true)
    }

    @objc private func addSampleButtonTapped() {
        let alert = UIAlertController(title: "Add Sample", message: "Choose how to create a new sample", preferredStyle: .actionSheet)

        alert.addAction(UIAlertAction(title: "Create New Sample", style: .default) { [weak self] _ in
            self?.createNewSample()
        })

        alert.addAction(UIAlertAction(title: "Copy from Crack Records", style: .default) { [weak self] _ in
            self?.showCrackRecordSelector()
        })

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))

        if let popover = alert.popoverPresentationController {
            popover.sourceView = addSampleButton
            popover.sourceRect = addSampleButton.bounds
        }

        present(alert, animated: true)
    }

    @objc private func compareButtonTapped() {
        let compareVC = ExperimentComparisonViewController(experimentGroup: experimentGroup)
        let navController = UINavigationController(rootViewController: compareVC)
        present(navController, animated: true)
    }

    // MARK: - Helper Methods
    private func createNewSample() {
        let alert = UIAlertController(title: "New Sample", message: "Enter sample details", preferredStyle: .alert)

        alert.addTextField { textField in
            textField.placeholder = "Sample name"
            textField.autocapitalizationType = .words
        }

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        alert.addAction(UIAlertAction(title: "Experimental", style: .default) { [weak self] _ in
            guard let name = alert.textFields?.first?.text, !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
                return
            }

            let sample = ExperimentSample(name: name, sampleType: .experimental)
            self?.experimentGroup.experimentSamples.append(sample)
            self?.experimentGroup.updateTimestamp()
            self?.updateUI()
        })

        alert.addAction(UIAlertAction(title: "Control", style: .default) { [weak self] _ in
            guard let name = alert.textFields?.first?.text, !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
                return
            }

            let sample = ExperimentSample(name: name, sampleType: .control)
            self?.experimentGroup.experimentSamples.append(sample)
            self?.experimentGroup.updateTimestamp()
            self?.updateUI()
        })

        present(alert, animated: true)
    }

    private func showCrackRecordSelector() {
        // Load existing crack records
        let userDefaults = UserDefaults.standard
        let recordsKey = "CrackRecords"

        var crackRecords: [CrackRecord] = []
        if let data = userDefaults.data(forKey: recordsKey),
           let records = try? NSKeyedUnarchiver.unarchiveTopLevelObjectWithData(data) as? [CrackRecord] {
            crackRecords = records
        }

        if crackRecords.isEmpty {
            let alert = UIAlertController(title: "No Records", message: "No crack records found. Create some crack records first.", preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "OK", style: .default))
            present(alert, animated: true)
            return
        }

        let selectorVC = CrackRecordSelectorViewController(crackRecords: crackRecords) { [weak self] selectedRecord in
            self?.createSampleFromRecord(selectedRecord)
        }

        let navController = UINavigationController(rootViewController: selectorVC)
        present(navController, animated: true)
    }

    private func createSampleFromRecord(_ record: CrackRecord) {
        let alert = UIAlertController(title: "Create Sample", message: "Choose sample type for '\(record.paintName)'", preferredStyle: .actionSheet)

        alert.addAction(UIAlertAction(title: "Experimental", style: .default) { [weak self] _ in
            let sample = ExperimentSample(name: record.paintName, sampleType: .experimental, baseRecord: record)
            self?.experimentGroup.experimentSamples.append(sample)
            self?.experimentGroup.updateTimestamp()
            self?.updateUI()
        })

        alert.addAction(UIAlertAction(title: "Control", style: .default) { [weak self] _ in
            let sample = ExperimentSample(name: record.paintName, sampleType: .control, baseRecord: record)
            self?.experimentGroup.experimentSamples.append(sample)
            self?.experimentGroup.updateTimestamp()
            self?.updateUI()
        })

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))

        if let popover = alert.popoverPresentationController {
            popover.sourceView = view
            popover.sourceRect = CGRect(x: view.bounds.midX, y: view.bounds.midY, width: 0, height: 0)
            popover.permittedArrowDirections = []
        }

        present(alert, animated: true)
    }
}

// MARK: - UITableViewDataSource
extension ExperimentDetailViewController: UITableViewDataSource {

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return max(experimentGroup.experimentSamples.count, 1) // Show at least 1 for empty state
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: ExperimentSampleCell.identifier, for: indexPath) as! ExperimentSampleCell

        if experimentGroup.experimentSamples.isEmpty {
            cell.configureEmptyState()
        } else {
            cell.configure(with: experimentGroup.experimentSamples[indexPath.row])
        }

        return cell
    }
}

// MARK: - UITableViewDelegate
extension ExperimentDetailViewController: UITableViewDelegate {

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return experimentGroup.experimentSamples.isEmpty ? 100 : 80
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)

        guard !experimentGroup.experimentSamples.isEmpty else { return }

        let sample = experimentGroup.experimentSamples[indexPath.row]
        let sampleDetailVC = ExperimentSampleDetailViewController(sample: sample)
        sampleDetailVC.delegate = self
        let navController = UINavigationController(rootViewController: sampleDetailVC)
        present(navController, animated: true)
    }

    func tableView(_ tableView: UITableView, commit editingStyle: UITableViewCell.EditingStyle, forRowAt indexPath: IndexPath) {
        if editingStyle == .delete && !experimentGroup.experimentSamples.isEmpty {
            experimentGroup.experimentSamples.remove(at: indexPath.row)
            experimentGroup.updateTimestamp()
            updateUI()
        }
    }
}

// MARK: - ExperimentSampleDetailDelegate
extension ExperimentDetailViewController: ExperimentSampleDetailDelegate {

    func didUpdateSample(_ sample: ExperimentSample) {
        if let index = experimentGroup.experimentSamples.firstIndex(where: { $0.id == sample.id }) {
            experimentGroup.experimentSamples[index] = sample
            experimentGroup.updateTimestamp()
            updateUI()
        }
    }
}
