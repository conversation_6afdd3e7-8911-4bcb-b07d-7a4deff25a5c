//
//  ExperimentSampleDetailViewController.swift
//  PaintCrackLog
//
//  Created by tyu<PERSON> on 2025/6/2.
//

import UIKit
import SnapKit

protocol ExperimentSampleDetailDelegate: AnyObject {
    func didUpdateSample(_ sample: ExperimentSample)
}

class ExperimentSampleDetailViewController: UIViewController {
    
    // MARK: - Properties
    weak var delegate: ExperimentSampleDetailDelegate?
    private var sample: ExperimentSample
    
    // MARK: - UI Components
    private lazy var gradientView: GradientView = {
        let view = GradientView(
            startColor: .secondaryGradientStart,
            endColor: .secondaryGradientEnd,
            direction: .topToBottom
        )
        return view
    }()
    
    private lazy var scrollView: UIScrollView = {
        let scroll = UIScrollView()
        scroll.backgroundColor = .clear
        scroll.showsVerticalScrollIndicator = false
        return scroll
    }()
    
    private lazy var contentView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    private lazy var formContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .cardBackground
        view.layer.cornerRadius = 20
        view.layer.shadowColor = UIColor.cardShadow.cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: 4)
        view.layer.shadowRadius = 12
        view.layer.shadowOpacity = 1.0
        return view
    }()
    
    // Form Fields
    private lazy var nameTextField: UITextField = {
        let field = createTextField(placeholder: "Sample Name")
        return field
    }()
    
    private lazy var temperatureTextField: UITextField = {
        let field = createTextField(placeholder: "Temperature (°C)")
        field.keyboardType = .decimalPad
        return field
    }()
    
    private lazy var humidityTextField: UITextField = {
        let field = createTextField(placeholder: "Humidity (%)")
        field.keyboardType = .decimalPad
        return field
    }()
    
    private lazy var thicknessTextField: UITextField = {
        let field = createTextField(placeholder: "Layer Thickness (mm)")
        field.keyboardType = .decimalPad
        return field
    }()
    
    private lazy var dryingTimeTextField: UITextField = {
        let field = createTextField(placeholder: "Drying Time (hours)")
        field.keyboardType = .decimalPad
        return field
    }()
    
    private lazy var notesTextView: UITextView = {
        let textView = UITextView()
        textView.backgroundColor = UIColor.systemGray6
        textView.layer.cornerRadius = 12
        textView.font = .systemFont(ofSize: 16)
        textView.textColor = .primaryText
        textView.textContainerInset = UIEdgeInsets(top: 12, left: 12, bottom: 12, right: 12)
        return textView
    }()
    
    // MARK: - Initialization
    init(sample: ExperimentSample) {
        self.sample = sample
        super.init(nibName: nil, bundle: nil)
        loadSampleData()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupKeyboardHandling()
    }
    
    // MARK: - Setup
    private func setupUI() {
        title = "Sample Details"
        view.backgroundColor = .systemBackground
        
        // Navigation items
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .cancel,
            target: self,
            action: #selector(cancelButtonTapped)
        )
        
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .save,
            target: self,
            action: #selector(saveButtonTapped)
        )
        
        // Add views
        view.addSubview(gradientView)
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        contentView.addSubview(formContainerView)
        
        // Add form fields
        let stackView = UIStackView(arrangedSubviews: [
            createFieldContainer(title: "Sample Name", field: nameTextField),
            createFieldContainer(title: "Temperature", field: temperatureTextField),
            createFieldContainer(title: "Humidity", field: humidityTextField),
            createFieldContainer(title: "Layer Thickness", field: thicknessTextField),
            createFieldContainer(title: "Drying Time", field: dryingTimeTextField),
            createNotesContainer()
        ])
        
        stackView.axis = .vertical
        stackView.spacing = 20
        stackView.distribution = .fill
        
        formContainerView.addSubview(stackView)
        
        // Constraints
        gradientView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        formContainerView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().offset(-20)
        }
        
        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(20)
        }
    }
    
    private func setupKeyboardHandling() {
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(dismissKeyboard))
        view.addGestureRecognizer(tapGesture)
    }
    
    // MARK: - Helper Methods
    private func createTextField(placeholder: String) -> UITextField {
        let field = UITextField()
        field.placeholder = placeholder
        field.backgroundColor = UIColor.systemGray6
        field.layer.cornerRadius = 12
        field.font = .systemFont(ofSize: 16)
        field.textColor = .primaryText
        field.leftView = UIView(frame: CGRect(x: 0, y: 0, width: 12, height: 0))
        field.leftViewMode = .always
        field.rightView = UIView(frame: CGRect(x: 0, y: 0, width: 12, height: 0))
        field.rightViewMode = .always
        return field
    }
    
    private func createFieldContainer(title: String, field: UIView) -> UIView {
        let container = UIView()
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = .systemFont(ofSize: 14, weight: .medium)
        titleLabel.textColor = .secondaryText
        
        container.addSubview(titleLabel)
        container.addSubview(field)
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
        }
        
        field.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalTo(48)
        }
        
        return container
    }
    
    private func createNotesContainer() -> UIView {
        let container = UIView()
        
        let titleLabel = UILabel()
        titleLabel.text = "Additional Notes"
        titleLabel.font = .systemFont(ofSize: 14, weight: .medium)
        titleLabel.textColor = .secondaryText
        
        container.addSubview(titleLabel)
        container.addSubview(notesTextView)
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
        }
        
        notesTextView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalTo(100)
        }
        
        return container
    }
    
    private func loadSampleData() {
        nameTextField.text = sample.name
        
        let conditions = sample.environmentalConditions
        if let temp = conditions.temperature {
            temperatureTextField.text = String(temp)
        }
        if let humidity = conditions.humidity {
            humidityTextField.text = String(humidity)
        }
        if let thickness = conditions.layerThickness {
            thicknessTextField.text = String(thickness)
        }
        if let dryingTime = conditions.dryingTime {
            dryingTimeTextField.text = String(dryingTime)
        }
        
        notesTextView.text = conditions.additionalNotes
    }
    
    // MARK: - Actions
    @objc private func cancelButtonTapped() {
        dismiss(animated: true)
    }
    
    @objc private func saveButtonTapped() {
        guard let name = nameTextField.text, !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            showAlert(title: "Error", message: "Please enter a sample name.")
            return
        }
        
        sample.name = name
        
        // Update environmental conditions
        sample.environmentalConditions.temperature = Double(temperatureTextField.text ?? "")
        sample.environmentalConditions.humidity = Double(humidityTextField.text ?? "")
        sample.environmentalConditions.layerThickness = Double(thicknessTextField.text ?? "")
        sample.environmentalConditions.dryingTime = Double(dryingTimeTextField.text ?? "")
        sample.environmentalConditions.additionalNotes = notesTextView.text ?? ""
        
        sample.updateTimestamp()
        delegate?.didUpdateSample(sample)
        dismiss(animated: true)
    }
    
    @objc private func dismissKeyboard() {
        view.endEditing(true)
    }
    
    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }
}
