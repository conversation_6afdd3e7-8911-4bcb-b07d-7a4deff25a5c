//
//  CrackRecordCell.swift
//  PaintCrackLog
//
//  Created by tyuu on 2025/6/2.
//

import UIKit
import SnapKit

class CrackRecordCell: UITableViewCell {
    
    static let identifier = "CrackRecordCell"
    
    // MARK: - UI Components
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .cardBackground
        view.layer.cornerRadius = 16
        view.layer.shadowColor = UIColor.cardShadow.cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowRadius = 8
        view.layer.shadowOpacity = 1.0
        return view
    }()
    
    private lazy var paintNameLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 18, weight: .semibold)
        label.textColor = .primaryText
        label.numberOfLines = 1
        return label
    }()
    
    private lazy var paintTypeLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .medium)
        label.textColor = .secondaryText
        label.numberOfLines = 1
        return label
    }()
    
    private lazy var surfaceLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .regular)
        label.textColor = .secondaryText
        label.numberOfLines = 1
        return label
    }()
    
    private lazy var statusView: UIView = {
        let view = UIView()
        view.layer.cornerRadius = 8
        return view
    }()
    
    private lazy var statusLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12, weight: .semibold)
        label.textColor = .white
        label.textAlignment = .center
        return label
    }()
    
    private lazy var crackPatternIcon: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.tintColor = .secondaryText
        return imageView
    }()
    
    private lazy var timeLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12, weight: .regular)
        label.textColor = .tertiaryText
        label.textAlignment = .right
        return label
    }()
    
    private lazy var crackTimeLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12, weight: .medium)
        label.textColor = .secondaryText
        label.textAlignment = .right
        return label
    }()
    
    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - Setup
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        contentView.addSubview(containerView)
        
        statusView.addSubview(statusLabel)
        
        containerView.addSubview(paintNameLabel)
        containerView.addSubview(paintTypeLabel)
        containerView.addSubview(surfaceLabel)
        containerView.addSubview(statusView)
        containerView.addSubview(crackPatternIcon)
        containerView.addSubview(timeLabel)
        containerView.addSubview(crackTimeLabel)
        
        containerView.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview().inset(8)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        paintNameLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.leading.equalToSuperview().offset(16)
            make.trailing.equalTo(statusView.snp.leading).offset(-8)
        }
        
        paintTypeLabel.snp.makeConstraints { make in
            make.top.equalTo(paintNameLabel.snp.bottom).offset(4)
            make.leading.equalTo(paintNameLabel)
            make.trailing.equalTo(paintNameLabel)
        }
        
        surfaceLabel.snp.makeConstraints { make in
            make.top.equalTo(paintTypeLabel.snp.bottom).offset(2)
            make.leading.equalTo(paintNameLabel)
            make.trailing.equalTo(paintNameLabel)
        }
        
        statusView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.trailing.equalToSuperview().offset(-16)
            make.width.equalTo(80)
            make.height.equalTo(24)
        }
        
        statusLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(4)
        }
        
        crackPatternIcon.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(-16)
            make.leading.equalTo(paintNameLabel)
            make.width.height.equalTo(20)
        }
        
        timeLabel.snp.makeConstraints { make in
            make.bottom.equalTo(crackTimeLabel.snp.top).offset(-2)
            make.trailing.equalToSuperview().offset(-16)
        }
        
        crackTimeLabel.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(-16)
            make.trailing.equalToSuperview().offset(-16)
        }
    }
    
    // MARK: - Configuration
    func configure(with record: CrackRecord) {
        paintNameLabel.text = record.paintName
        paintTypeLabel.text = "\(record.paintType.displayName) Paint"
        surfaceLabel.text = "on \(record.surfaceType.displayName)"
        
        // Status
        statusLabel.text = record.crackStatus.displayName
        switch record.crackStatus {
        case .notStarted:
            statusView.backgroundColor = .statusGreen
        case .inProgress:
            statusView.backgroundColor = .statusOrange
        case .stabilized:
            statusView.backgroundColor = .statusBlue
        case .worsening:
            statusView.backgroundColor = .statusRed
        }
        
        // Crack pattern icon
        crackPatternIcon.image = UIImage(systemName: record.crackPattern.icon)
        
        // Time information
        timeLabel.text = "Applied: \(record.applicationTime.shortDateString)"
        
        if let crackStartTime = record.crackStartTime {
            crackTimeLabel.text = "Cracked: \(record.formattedTimeToCrack)"
        } else {
            crackTimeLabel.text = "No cracking yet"
        }
    }
    
    // MARK: - Reuse
    override func prepareForReuse() {
        super.prepareForReuse()
        paintNameLabel.text = nil
        paintTypeLabel.text = nil
        surfaceLabel.text = nil
        statusLabel.text = nil
        crackPatternIcon.image = nil
        timeLabel.text = nil
        crackTimeLabel.text = nil
    }
}
