//
//  ExperimentGroupCell.swift
//  PaintCrackLog
//
//  Created by tyu<PERSON> on 2025/6/2.
//

import UIKit
import SnapKit

class ExperimentGroupCell: UITableViewCell {
    
    static let identifier = "ExperimentGroupCell"
    
    // MARK: - UI Components
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .cardBackground
        view.layer.cornerRadius = 16
        view.layer.shadowColor = UIColor.cardShadow.cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowRadius = 8
        view.layer.shadowOpacity = 1.0
        return view
    }()
    
    private lazy var nameLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 18, weight: .semibold)
        label.textColor = .primaryText
        label.numberOfLines = 1
        return label
    }()
    
    private lazy var descriptionLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .regular)
        label.textColor = .secondaryText
        label.numberOfLines = 2
        return label
    }()
    
    private lazy var samplesCountLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12, weight: .medium)
        label.textColor = .tertiaryText
        return label
    }()
    
    private lazy var experimentalBadge: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBlue
        view.layer.cornerRadius = 8
        return view
    }()
    
    private lazy var experimentalLabel: UILabel = {
        let label = UILabel()
        label.text = "EXP"
        label.font = .systemFont(ofSize: 10, weight: .bold)
        label.textColor = .white
        label.textAlignment = .center
        return label
    }()
    
    private lazy var controlBadge: UIView = {
        let view = UIView()
        view.backgroundColor = .systemOrange
        view.layer.cornerRadius = 8
        return view
    }()
    
    private lazy var controlLabel: UILabel = {
        let label = UILabel()
        label.text = "CTRL"
        label.font = .systemFont(ofSize: 10, weight: .bold)
        label.textColor = .white
        label.textAlignment = .center
        return label
    }()
    
    private lazy var timeLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12, weight: .regular)
        label.textColor = .tertiaryText
        label.textAlignment = .right
        return label
    }()
    
    private lazy var chevronImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "chevron.right")
        imageView.tintColor = .secondaryText
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - Setup
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        contentView.addSubview(containerView)
        
        experimentalBadge.addSubview(experimentalLabel)
        controlBadge.addSubview(controlLabel)
        
        containerView.addSubview(nameLabel)
        containerView.addSubview(descriptionLabel)
        containerView.addSubview(samplesCountLabel)
        containerView.addSubview(experimentalBadge)
        containerView.addSubview(controlBadge)
        containerView.addSubview(timeLabel)
        containerView.addSubview(chevronImageView)
        
        containerView.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview().inset(8)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        nameLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.leading.equalToSuperview().offset(16)
            make.trailing.equalTo(chevronImageView.snp.leading).offset(-8)
        }
        
        descriptionLabel.snp.makeConstraints { make in
            make.top.equalTo(nameLabel.snp.bottom).offset(4)
            make.leading.equalTo(nameLabel)
            make.trailing.equalTo(nameLabel)
        }
        
        samplesCountLabel.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(-16)
            make.leading.equalTo(nameLabel)
        }
        
        experimentalBadge.snp.makeConstraints { make in
            make.bottom.equalTo(samplesCountLabel)
            make.leading.equalTo(samplesCountLabel.snp.trailing).offset(12)
            make.width.equalTo(32)
            make.height.equalTo(16)
        }
        
        experimentalLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        controlBadge.snp.makeConstraints { make in
            make.bottom.equalTo(samplesCountLabel)
            make.leading.equalTo(experimentalBadge.snp.trailing).offset(8)
            make.width.equalTo(32)
            make.height.equalTo(16)
        }
        
        controlLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        timeLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.trailing.equalTo(chevronImageView.snp.leading).offset(-8)
        }
        
        chevronImageView.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.trailing.equalToSuperview().offset(-16)
            make.width.height.equalTo(16)
        }
    }
    
    // MARK: - Configuration
    func configure(with group: ExperimentGroup) {
        nameLabel.text = group.name
        descriptionLabel.text = group.experimentDescription.isEmpty ? "No description" : group.experimentDescription
        
        let experimentalCount = group.experimentSamples.filter { $0.sampleType == .experimental }.count
        let controlCount = group.experimentSamples.filter { $0.sampleType == .control }.count
        
        samplesCountLabel.text = "\(group.experimentSamples.count) samples"
        
        // Show/hide badges based on sample types
        experimentalBadge.isHidden = experimentalCount == 0
        controlBadge.isHidden = controlCount == 0
        
        timeLabel.text = group.createdAt.relativeString
    }
    
    // MARK: - Reuse
    override func prepareForReuse() {
        super.prepareForReuse()
        nameLabel.text = nil
        descriptionLabel.text = nil
        samplesCountLabel.text = nil
        timeLabel.text = nil
        experimentalBadge.isHidden = false
        controlBadge.isHidden = false
    }
}
