//
//  ExperimentSampleCell.swift
//  PaintCrackLog
//
//  Created by tyu<PERSON> on 2025/6/2.
//

import UIKit
import SnapKit

class ExperimentSampleCell: UITableViewCell {
    
    static let identifier = "ExperimentSampleCell"
    
    // MARK: - UI Components
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    private lazy var nameLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16, weight: .semibold)
        label.textColor = .primaryText
        label.numberOfLines = 1
        return label
    }()
    
    private lazy var typeLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12, weight: .medium)
        label.textColor = .white
        label.textAlignment = .center
        label.layer.cornerRadius = 8
        label.clipsToBounds = true
        return label
    }()
    
    private lazy var conditionsLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .regular)
        label.textColor = .secondaryText
        label.numberOfLines = 2
        return label
    }()
    
    private lazy var statusIcon: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.tintColor = .secondaryText
        return imageView
    }()
    
    private lazy var chevronImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "chevron.right")
        imageView.tintColor = .tertiaryText
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - Setup
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        contentView.addSubview(containerView)
        
        containerView.addSubview(nameLabel)
        containerView.addSubview(typeLabel)
        containerView.addSubview(conditionsLabel)
        containerView.addSubview(statusIcon)
        containerView.addSubview(chevronImageView)
        
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(8)
        }
        
        statusIcon.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(20)
        }
        
        nameLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.leading.equalTo(statusIcon.snp.trailing).offset(12)
            make.trailing.equalTo(typeLabel.snp.leading).offset(-8)
        }
        
        conditionsLabel.snp.makeConstraints { make in
            make.top.equalTo(nameLabel.snp.bottom).offset(4)
            make.leading.equalTo(nameLabel)
            make.trailing.equalTo(chevronImageView.snp.leading).offset(-8)
            make.bottom.equalToSuperview().offset(-12)
        }
        
        typeLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.trailing.equalTo(chevronImageView.snp.leading).offset(-12)
            make.width.equalTo(60)
            make.height.equalTo(20)
        }
        
        chevronImageView.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.trailing.equalToSuperview().offset(-16)
            make.width.height.equalTo(12)
        }
    }
    
    // MARK: - Configuration
    func configure(with sample: ExperimentSample) {
        nameLabel.text = sample.name
        
        // Configure type label
        typeLabel.text = sample.sampleType == .experimental ? "EXP" : "CTRL"
        typeLabel.backgroundColor = sample.sampleType == .experimental ? .systemBlue : .systemOrange
        
        // Configure status icon
        statusIcon.image = UIImage(systemName: sample.sampleType.icon)
        statusIcon.tintColor = sample.sampleType == .experimental ? .systemBlue : .systemOrange
        
        // Configure conditions text
        var conditionsText = ""
        let conditions = sample.environmentalConditions
        
        var conditionParts: [String] = []
        
        if let temp = conditions.temperature {
            conditionParts.append("Temp: \(temp)°C")
        }
        
        if let humidity = conditions.humidity {
            conditionParts.append("Humidity: \(humidity)%")
        }
        
        if let thickness = conditions.layerThickness {
            conditionParts.append("Thickness: \(thickness)mm")
        }
        
        if let dryingTime = conditions.dryingTime {
            conditionParts.append("Drying: \(dryingTime)h")
        }
        
        if conditionParts.isEmpty {
            conditionsText = "No conditions set"
        } else {
            conditionsText = conditionParts.joined(separator: " • ")
        }
        
        conditionsLabel.text = conditionsText
    }
    
    func configureEmptyState() {
        nameLabel.text = "No samples yet"
        typeLabel.isHidden = true
        conditionsLabel.text = "Tap 'Add Sample' to create your first experiment sample"
        statusIcon.image = UIImage(systemName: "plus.circle.dashed")
        statusIcon.tintColor = .tertiaryText
        chevronImageView.isHidden = true
    }
    
    // MARK: - Reuse
    override func prepareForReuse() {
        super.prepareForReuse()
        nameLabel.text = nil
        conditionsLabel.text = nil
        typeLabel.isHidden = false
        chevronImageView.isHidden = false
        statusIcon.image = nil
        statusIcon.tintColor = .secondaryText
    }
}
