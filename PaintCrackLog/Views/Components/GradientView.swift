//
//  GradientView.swift
//  PaintCrackLog
//
//  Created by tyuu on 2025/6/2.
//

import UIKit

class GradientView: UIView {
    
    // MARK: - Properties
    private let gradientLayer = CAGradientLayer()
    
    var startColor: UIColor = .primaryGradientStart {
        didSet {
            updateGradient()
        }
    }
    
    var endColor: UIColor = .primaryGradientEnd {
        didSet {
            updateGradient()
        }
    }
    
    var direction: GradientDirection = .topToBottom {
        didSet {
            updateGradient()
        }
    }
    
    // MARK: - Gradient Direction
    enum GradientDirection {
        case topToBottom
        case leftToRight
        case topLeftToBottomRight
        case topRightToBottomLeft
        
        var points: (start: CGPoint, end: CGPoint) {
            switch self {
            case .topToBottom:
                return (CGPoint(x: 0.5, y: 0), CGPoint(x: 0.5, y: 1))
            case .leftToRight:
                return (CGPoint(x: 0, y: 0.5), CGPoint(x: 1, y: 0.5))
            case .topLeftToBottomRight:
                return (CGPoint(x: 0, y: 0), CGPoint(x: 1, y: 1))
            case .topRightToBottomLeft:
                return (CGPoint(x: 1, y: 0), CGPoint(x: 0, y: 1))
            }
        }
    }
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupGradient()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupGradient()
    }
    
    convenience init(startColor: UIColor, endColor: UIColor, direction: GradientDirection = .topToBottom) {
        self.init(frame: .zero)
        self.startColor = startColor
        self.endColor = endColor
        self.direction = direction
        updateGradient()
    }
    
    // MARK: - Layout
    override func layoutSubviews() {
        super.layoutSubviews()
        gradientLayer.frame = bounds
    }
    
    // MARK: - Setup
    private func setupGradient() {
        layer.insertSublayer(gradientLayer, at: 0)
        updateGradient()
    }
    
    private func updateGradient() {
        gradientLayer.colors = [startColor.cgColor, endColor.cgColor]
        gradientLayer.startPoint = direction.points.start
        gradientLayer.endPoint = direction.points.end
    }
    
    // MARK: - Animation
    func animateGradient(to newStartColor: UIColor, endColor newEndColor: UIColor, duration: TimeInterval = 0.3) {
        let startColorAnimation = CABasicAnimation(keyPath: "colors")
        startColorAnimation.fromValue = gradientLayer.colors
        startColorAnimation.toValue = [newStartColor.cgColor, newEndColor.cgColor]
        startColorAnimation.duration = duration
        startColorAnimation.fillMode = .forwards
        startColorAnimation.isRemovedOnCompletion = false
        
        gradientLayer.add(startColorAnimation, forKey: "colorChange")
        
        // Update the actual values
        startColor = newStartColor
        endColor = newEndColor
    }
}
