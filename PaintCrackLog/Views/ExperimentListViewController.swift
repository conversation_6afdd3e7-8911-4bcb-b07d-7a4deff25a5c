//
//  ExperimentListViewController.swift
//  PaintCrackLog
//
//  Created by tyu<PERSON> on 2025/6/2.
//

import UIKit
import SnapKit

class ExperimentListViewController: UIViewController {
    
    // MARK: - Properties
    private var experimentGroups: [ExperimentGroup] = []
    private let userDefaults = UserDefaults.standard
    private let experimentsKey = "ExperimentGroups"
    
    // MARK: - UI Components
    private lazy var gradientView: GradientView = {
        let view = GradientView(
            startColor: .secondaryGradientStart,
            endColor: .secondaryGradientEnd,
            direction: .topToBottom
        )
        return view
    }()
    
    private lazy var tableView: UITableView = {
        let table = UITableView(frame: .zero, style: .plain)
        table.delegate = self
        table.dataSource = self
        table.backgroundColor = .clear
        table.separatorStyle = .none
        table.showsVerticalScrollIndicator = false
        table.register(ExperimentGroupCell.self, forCellReuseIdentifier: ExperimentGroupCell.identifier)
        table.contentInset = UIEdgeInsets(top: 20, left: 0, bottom: 20, right: 0)
        return table
    }()
    
    private lazy var emptyStateView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        view.isHidden = true
        
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "flask.fill")
        imageView.tintColor = .white.withAlphaComponent(0.6)
        imageView.contentMode = .scaleAspectFit
        
        let titleLabel = UILabel()
        titleLabel.text = "No Experiments"
        titleLabel.font = .systemFont(ofSize: 24, weight: .semibold)
        titleLabel.textColor = .white
        titleLabel.textAlignment = .center
        
        let subtitleLabel = UILabel()
        subtitleLabel.text = "Create experiment groups to compare crack conditions"
        subtitleLabel.font = .systemFont(ofSize: 16, weight: .regular)
        subtitleLabel.textColor = .white.withAlphaComponent(0.8)
        subtitleLabel.textAlignment = .center
        subtitleLabel.numberOfLines = 0
        
        view.addSubview(imageView)
        view.addSubview(titleLabel)
        view.addSubview(subtitleLabel)
        
        imageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().offset(-60)
            make.width.height.equalTo(80)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(imageView.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        return view
    }()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadExperiments()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        loadExperiments()
        tableView.reloadData()
        updateEmptyState()
    }
    
    // MARK: - Setup
    private func setupUI() {
        title = "Experiments"
        view.backgroundColor = .systemBackground
        
        // Add gradient background
        view.addSubview(gradientView)
        view.addSubview(tableView)
        view.addSubview(emptyStateView)
        
        gradientView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        tableView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        emptyStateView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        // Navigation bar setup
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .add,
            target: self,
            action: #selector(addButtonTapped)
        )
    }
    
    // MARK: - Actions
    @objc private func addButtonTapped() {
        let alert = UIAlertController(title: "New Experiment Group", message: "Enter a name for your experiment group", preferredStyle: .alert)
        
        alert.addTextField { textField in
            textField.placeholder = "Experiment name"
            textField.autocapitalizationType = .words
        }
        
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        alert.addAction(UIAlertAction(title: "Create", style: .default) { [weak self] _ in
            guard let name = alert.textFields?.first?.text, !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
                return
            }
            
            let newGroup = ExperimentGroup(name: name, experimentDescription: "")
            self?.experimentGroups.insert(newGroup, at: 0)
            self?.saveExperiments()
            self?.tableView.reloadData()
            self?.updateEmptyState()
            
            // Navigate to experiment detail
            let detailVC = ExperimentDetailViewController(experimentGroup: newGroup)
            detailVC.delegate = self
            let navController = UINavigationController(rootViewController: detailVC)
            self?.present(navController, animated: true)
        })
        
        present(alert, animated: true)
    }
    
    // MARK: - Data Management
    private func loadExperiments() {
        if let data = userDefaults.data(forKey: experimentsKey),
           let groups = try? NSKeyedUnarchiver.unarchiveTopLevelObjectWithData(data) as? [ExperimentGroup] {
            experimentGroups = groups.sorted { $0.createdAt > $1.createdAt }
        }
    }
    
    private func saveExperiments() {
        if let data = try? NSKeyedArchiver.archivedData(withRootObject: experimentGroups, requiringSecureCoding: false) {
            userDefaults.set(data, forKey: experimentsKey)
        }
    }
    
    private func updateEmptyState() {
        emptyStateView.isHidden = !experimentGroups.isEmpty
        tableView.isHidden = experimentGroups.isEmpty
    }
}

// MARK: - UITableViewDataSource
extension ExperimentListViewController: UITableViewDataSource {

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return experimentGroups.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: ExperimentGroupCell.identifier, for: indexPath) as! ExperimentGroupCell
        cell.configure(with: experimentGroups[indexPath.row])
        return cell
    }
}

// MARK: - UITableViewDelegate
extension ExperimentListViewController: UITableViewDelegate {

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 120
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)

        let group = experimentGroups[indexPath.row]
        let detailVC = ExperimentDetailViewController(experimentGroup: group)
        detailVC.delegate = self
        let navController = UINavigationController(rootViewController: detailVC)
        present(navController, animated: true)
    }

    func tableView(_ tableView: UITableView, commit editingStyle: UITableViewCell.EditingStyle, forRowAt indexPath: IndexPath) {
        if editingStyle == .delete {
            experimentGroups.remove(at: indexPath.row)
            saveExperiments()
            tableView.deleteRows(at: [indexPath], with: .fade)
            updateEmptyState()
        }
    }
}

// MARK: - ExperimentDetailDelegate
extension ExperimentListViewController: ExperimentDetailDelegate {

    func didUpdateExperimentGroup(_ group: ExperimentGroup) {
        if let index = experimentGroups.firstIndex(where: { $0.id == group.id }) {
            experimentGroups[index] = group
        }

        experimentGroups.sort { $0.createdAt > $1.createdAt }
        saveExperiments()
        tableView.reloadData()
        updateEmptyState()
    }
}
