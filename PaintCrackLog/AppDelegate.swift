//
//  AppDelegate.swift
//  PaintCrackLog
//
//  Created by tyuu on 2025/6/2.
//

import UIKit

@main
class AppDelegate: UIResponder, UIApplicationDelegate {

    var window: UIWindow?

    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {

        window = UIWindow(frame: UIScreen.main.bounds)

        // Configure navigation bar appearance
        setupNavigationBarAppearance()

        // Create tab bar controller
        let tabBarController = UITabBarController()

        // Module 1: Crack Records
        let crackListVC = CrackRecordListViewController()
        let crackNavController = UINavigationController(rootViewController: crackListVC)
        crackNavController.tabBarItem = UITabBarItem(
            title: "Records",
            image: UIImage(systemName: "paintbrush"),
            selectedImage: UIImage(systemName: "paintbrush.fill")
        )

        // Module 2: Experiments
        let experimentListVC = ExperimentListViewController()
        let experimentNavController = UINavigationController(rootViewController: experimentListVC)
        experimentNavController.tabBarItem = UITabBarItem(
            title: "Experiments",
            image: UIImage(systemName: "flask"),
            selectedImage: UIImage(systemName: "flask.fill")
        )

        tabBarController.viewControllers = [crackNavController, experimentNavController]

        // Configure tab bar appearance
        setupTabBarAppearance()

        window?.rootViewController = tabBarController
        window?.makeKeyAndVisible()

        return true
    }

    private func setupNavigationBarAppearance() {
        let appearance = UINavigationBarAppearance()
        appearance.configureWithTransparentBackground()
        appearance.backgroundColor = UIColor.clear
        appearance.titleTextAttributes = [
            .foregroundColor: UIColor.white,
            .font: UIFont.systemFont(ofSize: 18, weight: .semibold)
        ]
        appearance.largeTitleTextAttributes = [
            .foregroundColor: UIColor.white,
            .font: UIFont.systemFont(ofSize: 32, weight: .bold)
        ]

        UINavigationBar.appearance().standardAppearance = appearance
        UINavigationBar.appearance().scrollEdgeAppearance = appearance
        UINavigationBar.appearance().compactAppearance = appearance
        UINavigationBar.appearance().tintColor = .white
    }

    private func setupTabBarAppearance() {
        let appearance = UITabBarAppearance()
        appearance.configureWithOpaqueBackground()
        appearance.backgroundColor = UIColor.systemBackground.withAlphaComponent(0.95)

        // Normal state
        appearance.stackedLayoutAppearance.normal.iconColor = .secondaryText
        appearance.stackedLayoutAppearance.normal.titleTextAttributes = [
            .foregroundColor: UIColor.secondaryText,
            .font: UIFont.systemFont(ofSize: 10, weight: .medium)
        ]

        // Selected state
        appearance.stackedLayoutAppearance.selected.iconColor = .systemBlue
        appearance.stackedLayoutAppearance.selected.titleTextAttributes = [
            .foregroundColor: UIColor.systemBlue,
            .font: UIFont.systemFont(ofSize: 10, weight: .semibold)
        ]

        UITabBar.appearance().standardAppearance = appearance
        if #available(iOS 15.0, *) {
            UITabBar.appearance().scrollEdgeAppearance = appearance
        }
    }
}

