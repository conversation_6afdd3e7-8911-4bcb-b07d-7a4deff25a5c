// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		95E7FD042DED8E7F00980E62 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95E7FCFA2DED8E7F00980E62 /* AppDelegate.swift */; };
		95E7FD072DED8E7F00980E62 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 95E7FCFB2DED8E7F00980E62 /* Assets.xcassets */; };
		95E7FD092DED8E7F00980E62 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 95E7FCFE2DED8E7F00980E62 /* LaunchScreen.storyboard */; };
		95E7FD172DED905200980E62 /* CrackEnums.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95E7FD0B2DED905200980E62 /* CrackEnums.swift */; };
		95E7FD182DED905200980E62 /* Date+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95E7FD0E2DED905200980E62 /* Date+Extensions.swift */; };
		95E7FD192DED905200980E62 /* UIColor+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95E7FD0F2DED905200980E62 /* UIColor+Extensions.swift */; };
		95E7FD1A2DED905200980E62 /* CrackRecordCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95E7FD112DED905200980E62 /* CrackRecordCell.swift */; };
		95E7FD1B2DED905200980E62 /* CrackRecordDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95E7FD142DED905200980E62 /* CrackRecordDetailViewController.swift */; };
		95E7FD1C2DED905200980E62 /* CrackRecordListViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95E7FD152DED905200980E62 /* CrackRecordListViewController.swift */; };
		95E7FD1D2DED905200980E62 /* GradientView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95E7FD122DED905200980E62 /* GradientView.swift */; };
		95E7FD1E2DED905200980E62 /* CrackRecord.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95E7FD0C2DED905200980E62 /* CrackRecord.swift */; };
		B3D7E7DB37EF6B5E0D02074D /* Pods_PaintCrackLog.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 571897F407F13CBA493A2E56 /* Pods_PaintCrackLog.framework */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		571897F407F13CBA493A2E56 /* Pods_PaintCrackLog.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_PaintCrackLog.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		95E7FCE22DED8E6700980E62 /* PaintCrackLog.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = PaintCrackLog.app; sourceTree = BUILT_PRODUCTS_DIR; };
		95E7FCFA2DED8E7F00980E62 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		95E7FCFB2DED8E7F00980E62 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		95E7FCFC2DED8E7F00980E62 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		95E7FCFD2DED8E7F00980E62 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		95E7FD0B2DED905200980E62 /* CrackEnums.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CrackEnums.swift; sourceTree = "<group>"; };
		95E7FD0C2DED905200980E62 /* CrackRecord.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CrackRecord.swift; sourceTree = "<group>"; };
		95E7FD0E2DED905200980E62 /* Date+Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Date+Extensions.swift"; sourceTree = "<group>"; };
		95E7FD0F2DED905200980E62 /* UIColor+Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UIColor+Extensions.swift"; sourceTree = "<group>"; };
		95E7FD112DED905200980E62 /* CrackRecordCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CrackRecordCell.swift; sourceTree = "<group>"; };
		95E7FD122DED905200980E62 /* GradientView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GradientView.swift; sourceTree = "<group>"; };
		95E7FD142DED905200980E62 /* CrackRecordDetailViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CrackRecordDetailViewController.swift; sourceTree = "<group>"; };
		95E7FD152DED905200980E62 /* CrackRecordListViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CrackRecordListViewController.swift; sourceTree = "<group>"; };
		B2DE7E0585A006BEA9DE8DB7 /* Pods-PaintCrackLog.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-PaintCrackLog.debug.xcconfig"; path = "Target Support Files/Pods-PaintCrackLog/Pods-PaintCrackLog.debug.xcconfig"; sourceTree = "<group>"; };
		E5AC8F3125A9309FAEF4DAB3 /* Pods-PaintCrackLog.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-PaintCrackLog.release.xcconfig"; path = "Target Support Files/Pods-PaintCrackLog/Pods-PaintCrackLog.release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		95E7FCDF2DED8E6700980E62 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B3D7E7DB37EF6B5E0D02074D /* Pods_PaintCrackLog.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		235FF9000F9207164F5B4B43 /* Pods */ = {
			isa = PBXGroup;
			children = (
				B2DE7E0585A006BEA9DE8DB7 /* Pods-PaintCrackLog.debug.xcconfig */,
				E5AC8F3125A9309FAEF4DAB3 /* Pods-PaintCrackLog.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		95E7FCD92DED8E6700980E62 = {
			isa = PBXGroup;
			children = (
				95E7FD032DED8E7F00980E62 /* PaintCrackLog */,
				95E7FCE32DED8E6700980E62 /* Products */,
				235FF9000F9207164F5B4B43 /* Pods */,
				C7842953C7831FA3BD3A24EB /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		95E7FCE32DED8E6700980E62 /* Products */ = {
			isa = PBXGroup;
			children = (
				95E7FCE22DED8E6700980E62 /* PaintCrackLog.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		95E7FD032DED8E7F00980E62 /* PaintCrackLog */ = {
			isa = PBXGroup;
			children = (
				95E7FD0D2DED905200980E62 /* Models */,
				95E7FD102DED905200980E62 /* Utils */,
				95E7FD162DED905200980E62 /* Views */,
				95E7FCFA2DED8E7F00980E62 /* AppDelegate.swift */,
				95E7FCFB2DED8E7F00980E62 /* Assets.xcassets */,
				95E7FCFC2DED8E7F00980E62 /* Info.plist */,
				95E7FCFE2DED8E7F00980E62 /* LaunchScreen.storyboard */,
			);
			path = PaintCrackLog;
			sourceTree = "<group>";
		};
		95E7FD0D2DED905200980E62 /* Models */ = {
			isa = PBXGroup;
			children = (
				95E7FD0B2DED905200980E62 /* CrackEnums.swift */,
				95E7FD0C2DED905200980E62 /* CrackRecord.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		95E7FD102DED905200980E62 /* Utils */ = {
			isa = PBXGroup;
			children = (
				95E7FD0E2DED905200980E62 /* Date+Extensions.swift */,
				95E7FD0F2DED905200980E62 /* UIColor+Extensions.swift */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		95E7FD132DED905200980E62 /* Components */ = {
			isa = PBXGroup;
			children = (
				95E7FD112DED905200980E62 /* CrackRecordCell.swift */,
				95E7FD122DED905200980E62 /* GradientView.swift */,
			);
			path = Components;
			sourceTree = "<group>";
		};
		95E7FD162DED905200980E62 /* Views */ = {
			isa = PBXGroup;
			children = (
				95E7FD132DED905200980E62 /* Components */,
				95E7FD142DED905200980E62 /* CrackRecordDetailViewController.swift */,
				95E7FD152DED905200980E62 /* CrackRecordListViewController.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		C7842953C7831FA3BD3A24EB /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				571897F407F13CBA493A2E56 /* Pods_PaintCrackLog.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		95E7FCE12DED8E6700980E62 /* PaintCrackLog */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 95E7FCF52DED8E6800980E62 /* Build configuration list for PBXNativeTarget "PaintCrackLog" */;
			buildPhases = (
				C6190CF168FB3166A78B7E53 /* [CP] Check Pods Manifest.lock */,
				95E7FCDE2DED8E6700980E62 /* Sources */,
				95E7FCDF2DED8E6700980E62 /* Frameworks */,
				95E7FCE02DED8E6700980E62 /* Resources */,
				3AA0BC739CB24AD949286AE0 /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = PaintCrackLog;
			productName = PaintCrackLog;
			productReference = 95E7FCE22DED8E6700980E62 /* PaintCrackLog.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		95E7FCDA2DED8E6700980E62 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					95E7FCE12DED8E6700980E62 = {
						CreatedOnToolsVersion = 16.3;
					};
				};
			};
			buildConfigurationList = 95E7FCDD2DED8E6700980E62 /* Build configuration list for PBXProject "PaintCrackLog" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 95E7FCD92DED8E6700980E62;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 95E7FCE32DED8E6700980E62 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				95E7FCE12DED8E6700980E62 /* PaintCrackLog */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		95E7FCE02DED8E6700980E62 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				95E7FD072DED8E7F00980E62 /* Assets.xcassets in Resources */,
				95E7FD092DED8E7F00980E62 /* LaunchScreen.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		3AA0BC739CB24AD949286AE0 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-PaintCrackLog/Pods-PaintCrackLog-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-PaintCrackLog/Pods-PaintCrackLog-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-PaintCrackLog/Pods-PaintCrackLog-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		C6190CF168FB3166A78B7E53 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-PaintCrackLog-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		95E7FCDE2DED8E6700980E62 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				95E7FD172DED905200980E62 /* CrackEnums.swift in Sources */,
				95E7FD182DED905200980E62 /* Date+Extensions.swift in Sources */,
				95E7FD192DED905200980E62 /* UIColor+Extensions.swift in Sources */,
				95E7FD1A2DED905200980E62 /* CrackRecordCell.swift in Sources */,
				95E7FD1B2DED905200980E62 /* CrackRecordDetailViewController.swift in Sources */,
				95E7FD1C2DED905200980E62 /* CrackRecordListViewController.swift in Sources */,
				95E7FD1D2DED905200980E62 /* GradientView.swift in Sources */,
				95E7FD1E2DED905200980E62 /* CrackRecord.swift in Sources */,
				95E7FD042DED8E7F00980E62 /* AppDelegate.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		95E7FCFE2DED8E7F00980E62 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				95E7FCFD2DED8E7F00980E62 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		95E7FCF62DED8E6800980E62 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B2DE7E0585A006BEA9DE8DB7 /* Pods-PaintCrackLog.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = PaintCrackLog/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.yiiguiugiapp.PaintCrackLog;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		95E7FCF72DED8E6800980E62 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E5AC8F3125A9309FAEF4DAB3 /* Pods-PaintCrackLog.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = PaintCrackLog/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.yiiguiugiapp.PaintCrackLog;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		95E7FCF82DED8E6800980E62 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		95E7FCF92DED8E6800980E62 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		95E7FCDD2DED8E6700980E62 /* Build configuration list for PBXProject "PaintCrackLog" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				95E7FCF82DED8E6800980E62 /* Debug */,
				95E7FCF92DED8E6800980E62 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		95E7FCF52DED8E6800980E62 /* Build configuration list for PBXNativeTarget "PaintCrackLog" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				95E7FCF62DED8E6800980E62 /* Debug */,
				95E7FCF72DED8E6800980E62 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 95E7FCDA2DED8E6700980E62 /* Project object */;
}
